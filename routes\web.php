<?php

use App\Http\Controllers\Attendance\AnnouncementController;
use App\Http\Controllers\Attendance\AttendanceController;
use App\Http\Controllers\Attendance\AttendanceNotificationController;
use App\Http\Controllers\Attendance\AttendanceReportController;
use App\Http\Controllers\Attendance\FixImagesController;
use App\Http\Controllers\Attendance\HolidayController;
use App\Http\Controllers\Attendance\LeaveController;
use App\Http\Controllers\Attendance\LeaveRequestController;
use App\Http\Controllers\Attendance\MachinesController;
use App\Http\Controllers\Attendance\StationController;
use App\Http\Controllers\Attendance\StudentsController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\Settings\PermissionController;
use App\Http\Controllers\Auth\Settings\PreRoleController;
use App\Http\Controllers\Auth\Settings\RoleController;
use App\Http\Controllers\Auth\Settings\UserController;
use App\Http\Controllers\CapacityBuilding\TrainerController;
use App\Http\Controllers\CapacityBuilding\TrainingController;
use App\Http\Controllers\Card\CardController;
use App\Http\Controllers\Errors\ErrorsMainController;
use App\Http\Controllers\General\LangController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\MasterData\AcademicFieldCategoryController;
use App\Http\Controllers\MasterData\AcademicFieldController;
use App\Http\Controllers\MasterData\AskariTypeController;
use App\Http\Controllers\MasterData\CardPeriodController;
use App\Http\Controllers\MasterData\BloodGroupController;
use App\Http\Controllers\MasterData\CardTypeController;
use App\Http\Controllers\MasterData\ContractCancellationReasonController;
use App\Http\Controllers\MasterData\ContractSuspensionReasonController;
use App\Http\Controllers\MasterData\CountryController;
use App\Http\Controllers\MasterData\DistrictController;
use App\Http\Controllers\MasterData\EducationDegreeController;
use App\Http\Controllers\MasterData\EmployeeEvaluationResultController;
use App\Http\Controllers\MasterData\EmployeeQadamController;
use App\Http\Controllers\MasterData\EthnicityController;
use App\Http\Controllers\MasterData\FireReasonController;
use App\Http\Controllers\MasterData\GovernmentOfficeController;
use App\Http\Controllers\MasterData\GuaranteeRangeController;
use App\Http\Controllers\MasterData\GuaranteeTypeController;
use App\Http\Controllers\MasterData\LeaveTypeController;
use App\Http\Controllers\MasterData\MakafatTypeController;
use App\Http\Controllers\MasterData\MasterDataHomeController;
use App\Http\Controllers\MasterData\MoqarariApprovalAuthorityController;
use App\Http\Controllers\MasterData\ProvinceController;
use App\Http\Controllers\MasterData\PunishmentReasonController;
use App\Http\Controllers\MasterData\PunishmentTypeController;
use App\Http\Controllers\MasterData\TanqisReasonController;
use App\Http\Controllers\MasterData\TrainingTypeController;
use App\Http\Controllers\MasterData\TransferReasonController;
use App\Http\Controllers\MasterData\UniversityController;
use App\Http\Controllers\MasterData\VillageController;
use App\Http\Controllers\Recruitment\EmployeeAskariController;
use App\Http\Controllers\Recruitment\EmployeeAttachmentController;
use App\Http\Controllers\Recruitment\EmployeeContractController;
use App\Http\Controllers\Recruitment\EmployeeController;
use App\Http\Controllers\Recruitment\EmployeeEvaluationController;
use App\Http\Controllers\Recruitment\EmployeeExperienceController;
use App\Http\Controllers\Recruitment\EmployeeFireController;
use App\Http\Controllers\Recruitment\EmployeeGuaranteeController;
use App\Http\Controllers\Recruitment\EmployeeMakafatController;
use App\Http\Controllers\Recruitment\EmployeePromotionController;
use App\Http\Controllers\Recruitment\EmployeePunishmentController;
use App\Http\Controllers\Recruitment\EmployeeResignController;
use App\Http\Controllers\Recruitment\EmployeeRetireController;
use App\Http\Controllers\Recruitment\EmployeeSearchController;
use App\Http\Controllers\Recruitment\EmployeeServiceController;
use App\Http\Controllers\Recruitment\EmployeeTanqisController;
use App\Http\Controllers\Recruitment\EmployeeTrainingController;
use App\Http\Controllers\Recruitment\EmployeeTransferController;
use App\Http\Controllers\FormController;
use App\Http\Controllers\MasterData\EmployeeEvaluationRecommendationController;
use App\Http\Controllers\MasterData\FormController as MasterDataFormController;
use App\Http\Controllers\MasterData\FormsController;
use App\Http\Controllers\MasterData\JobDescriptionTemplateController;
use App\Http\Controllers\MasterData\LeaveRequestValidDateController;
use App\Http\Controllers\MasterData\NtaGridController;
use App\Http\Controllers\MasterData\NtaSalaryController;
use App\Http\Controllers\MasterData\NtaStepController;
use App\Http\Controllers\Recruitment\TemporaryEducationDocumentController;
use App\Http\Controllers\Tashkilat\Tashkilat\TashkilatController;
use App\Http\Controllers\Tashkilat\Department\DepartmentController;
use App\Http\Controllers\Tashkilat\Tashkilat\BilmaqtaTashkilatController;
use App\Http\Controllers\Tashkilat\Tashkilat\NtaTashkilatController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
      return redirect('/home');
})->name('/');
// languae Routes
Route::post('/changeLang', [LangController::class, 'changeLanguage'])->name('changeLang');
//end language Routes

Route::get('/home', [HomeController::class, 'index'])->name('home');

Route::prefix('errors')->group(function () {
      Route::get('not-allowed', [ErrorsMainController::class, 'notAllowed'])->name('not.allowed');
});

Route::middleware(['auth', 'throttle:200,1'])->group(function () {

      /*
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      | START DOCUMENTS ROUTES استخدام
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      */
      Route::prefix('document')->group(function () {
            Route::get('/employees/barhal', [EmployeeController::class, 'getBarhalEmployees'])->name('document.employees.barhal');
      });
      /*
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      | START DOCUMENTS ROUTES استخدام
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      */


      /*
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      | START RECRUITMENT ROUTES استخدام
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      */
      Route::prefix('recruitment')->group(function () {
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START EMPLOYEE MASTER DATA ROUTES 
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. EMPLOYEE, AJIR, MILITARY [TYPES, BASTS, RANKS]
            Route::get('/employee-types', [EmployeeController::class, 'getEmployeeTypes'])->name('employee.types');
            Route::get('/employee-types/rendered', [EmployeeController::class, 'getEmployeeTypesRendered'])->name('employee.types.rendered');
            Route::get('/employee-basts', [EmployeeController::class, 'getEmployeeBasts'])->name('employee.basts');
            Route::get('/employee-basts/rendered', [EmployeeController::class, 'getEmployeeBastsRendered'])->name('employee.basts.rendered');
            Route::get('/employee-ranks', [EmployeeController::class, 'getEmployeeRanks'])->name('employee.ranks');
            Route::get('/employee-ranks/rendered', [EmployeeController::class, 'getEmployeeRanksRendered'])->name('employee.ranks.rendered');
            Route::get('/ajir-basts', [EmployeeController::class, 'getAjirBasts'])->name('ajir.basts');
            Route::get('/ajir-basts/rendered', [EmployeeController::class, 'getAjirBastsRendered'])->name('ajir.basts.rendered');
            Route::get('/ajir-ranks', [EmployeeController::class, 'getAjirRanks'])->name('ajir.ranks');
            Route::get('/ajir-ranks/rendered', [EmployeeController::class, 'getAjirRanksRendered'])->name('ajir.ranks.rendered');
            Route::get('/military-basts', [EmployeeController::class, 'getMilitaryBasts'])->name('military.basts');
            Route::get('/military-basts/rendered', [EmployeeController::class, 'getMilitaryBastsRendered'])->name('military.basts.rendered');
            Route::get('/military-ranks', [EmployeeController::class, 'getMilitaryRanks'])->name('military.ranks');
            Route::get('/military-ranks/rendered', [EmployeeController::class, 'getMilitaryRanksRendered'])->name('military.ranks.rendered');
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END EMPLOYEE MASTER DATA ROUTES
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */


            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START BARHAL EMPLOYEE ROUTES برحال یا اصلی
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employees/barhal', [EmployeeController::class, 'getBarhalEmployees'])->name('recruitment.employees.barhal');
            Route::get('/employees/barhal/search', [EmployeeController::class, 'searchAllEmployees'])->name('recruitment.employees.barhal.search');
            Route::get('/employees/barhal/report/excel', [EmployeeController::class, 'exportAllEmployeesSearchToExcel'])->name('recruitment.employees.barhal.report.excel');
            Route::get('/employees/barhal/with-tin/report/excel', [EmployeeController::class, 'exportAllEmployeesWithTinToExcel'])->name('recruitment.employees.barhal.report.excel.with-tin');
            Route::get('/employees/barhal/without-tin/report/excel', [EmployeeController::class, 'exportAllEmployeesWithoutTinToExcel'])->name('recruitment.employees.barhal.report.excel.without-tin');
            Route::get('/employees/barhal/count', [EmployeeController::class, 'getAllEmployeesCount'])->name('recruitment.employees.barhal.all.count');
            Route::get('/employees/barhal/employees/count', [EmployeeController::class, 'getEmployeesCount'])->name('recruitment.employees.barhal.employees.count');
            Route::get('/employees/barhal/ajirs/count', [EmployeeController::class, 'getAjirsCount'])->name('recruitment.employees.barhal.ajirs.count');
            Route::get('/employees/barhal/militaries/count', [EmployeeController::class, 'getMilitariesCount'])->name('recruitment.employees.barhal.militaries.count');

            Route::post('/employee/specifications/tab', [EmployeeController::class, 'getEmployeeSpecificationsTab'])->name('recruitment.employee.specifications.tab');

            // 2. EDIT & UPDATE ROUTES
            Route::post('/employee/personal/update', [EmployeeController::class, 'updatePersonalInfo'])->name('recruitment.employee.personal.update');
            Route::get('/employee/tainat/history', [EmployeeController::class, 'getTainatHistory'])->name('recruitment.employee.tainat.history');
            Route::post('/employee/tainat/update', [EmployeeController::class, 'updateNewTainat'])->name('recruitment.employee.tainat.update');
            Route::post('/employee/english-info/update', [EmployeeController::class, 'updateEnglishInfo'])->name('recruitment.employee.english-info.update');
            Route::post('/employee/contact/update', [EmployeeController::class, 'updateContact'])->name('recruitment.employee.contact.update');
            Route::post('/employee/tazkira/update', [EmployeeController::class, 'updateTazkira'])->name('recruitment.employee.tazkira.update');
            Route::post('/employee/residence/update', [EmployeeController::class, 'updateResidence'])->name('recruitment.employee.residence.update');
            Route::get('/employee/educations', [EmployeeController::class, 'getEmployeeEducations'])->name('recruitment.employee.educations.get');
            Route::get('/employee/education/template', [EmployeeController::class, 'getNewEducationTemplate'])->name('recruitment.employee.education.template');
            Route::post('/employee/education/update', [EmployeeController::class, 'updateEducation'])->name('recruitment.employee.education.update');
            Route::post('/employee/education/store', [EmployeeController::class, 'storeEducation'])->name('recruitment.employee.education.store');
            Route::post('/employee/education/delete', [EmployeeController::class, 'deleteEducation'])->name('recruitment.employee.education.delete');
            Route::post('/employee/rank/update', [EmployeeController::class, 'updateRank'])->name('recruitment.employee.rank.update');
            Route::post('/employee/moqarari-approval-authority/update', [EmployeeController::class, 'updateHokmMoqarari'])->name('recruitment.employee.hokm-moqarari.update');
            Route::post('/employee/other/update', [EmployeeController::class, 'updateOtherInfo'])->name('recruitment.employee.other.update');
            Route::get('/employee/{id}/edit', [EmployeeController::class, 'showEditForm'])->name('recruitment.employee.edit');

            // 3. CREATE & STORE ROUTES
            Route::get('/employee/create', [EmployeeController::class, 'showCreateForm'])->name('recruitment.employee.create');
            Route::post('/employee/store', [EmployeeController::class, 'store'])->name('recruitment.employee.store');
            Route::get('/employee/service/in/create', [EmployeeController::class, 'showCreateForm'])->name('recruitment.employee.service.create');
            Route::post('/employee/service/in/store', [EmployeeController::class, 'storeServiceEmployee'])->name('recruitment.employee.service.store');
            Route::get('/employee/contract/in/create', [EmployeeController::class, 'showCreateForm'])->name('recruitment.employee.contract.create');
            Route::post('/employee/contract/in/store', [EmployeeController::class, 'storeContractEmployee'])->name('recruitment.employee.contract.store');
            Route::get('/employee/special/create', [EmployeeController::class, 'showCreateForm'])->name('recruitment.employee.special.create');
            Route::post('/employee/special/store', [EmployeeController::class, 'storeSpecialEmployee'])->name('recruitment.employee.special.store');

            // 4. DELETE ROUTES
            Route::delete('/employee/{id}/delete', [EmployeeController::class, 'delete'])->name('recruitment.employee.delete');
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END BARHAL EMPLOYEE ROUTES برحال یا اصلی
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */


            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START ADVANCED SEARCH ROUTES جستجوی پیشرفته
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employees/search/advanced/page', [EmployeeSearchController::class, 'index'])->name('recruitment.employees.search.advanced.index');
            Route::get('/employees/search/advanced/report/excel', [EmployeeSearchController::class, 'exportToExcel'])->name('recruitment.employees.search.advanced.report.excel');
            Route::get('/employees/search/advanced', [EmployeeSearchController::class, 'search'])->name('recruitment.employees.search.advanced');

            // 2. EDIT & UPDATE ROUTES

            // 3. CREATE & STORE ROUTES

            // 4. DELETE ROUTES
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END ADVANCED SEARCH ROUTES جستجوی پیشرفته
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */


            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START TRANSFER EMPLOYEES ROUTES کارمندان تبدیلی
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employees/transfer', [EmployeeTransferController::class, 'getTransferedEmployees'])->name('recruitment.employees.transfer');
            Route::get('/employees/transfer/search', [EmployeeTransferController::class, 'searchTransferedEmployees'])->name('recruitment.employees.transfer.search');
            Route::get('/employees/transfer/report/excel', [EmployeeTransferController::class, 'searchTransferedEmployeesForExcel'])->name('recruitment.employees.transfer.report.excel');
            Route::get('/employee/transfers', [EmployeeTransferController::class, 'getEmployeeTransfers'])->name('recruitment.employee.transfer.transfers.get');
            Route::get('/employee/transfers/tab', [EmployeeTransferController::class, 'getEmployeeTransfersTab'])->name('recruitment.employee.transfer.transfers.tab.get');
            Route::get('/employee/transfer', [EmployeeTransferController::class, 'getEmployeeTransferById'])->name('recruitment.employee.transfer.transfer.get');
            Route::get('/employees/transfer/all/count', [EmployeeTransferController::class, 'getTransferedCount'])->name('recruitment.employees.transfer.count');

            // 2. EDIT & UPDATE ROUTES
            Route::get('/employee/transfer/{id}/edit', [EmployeeTransferController::class, 'showEmployeeTransferEditForm'])->name('recruitment.employee.transfer.transfer.edit');
            Route::post('/employee/transfer/update', [EmployeeTransferController::class, 'update'])->name('recruitment.employee.transfer.transfer.update');
            Route::post('/employee/transfer/status/update', [EmployeeTransferController::class, 'updateStatus'])->name('recruitment.employee.transfer.transfer.status.update');

            // 3. CREATE & STORE ROUTES
            Route::post('/employee/transfer/store', [EmployeeTransferController::class, 'store'])->name('recruitment.employee.transfer.transfer.store');

            // 4. DELETE ROUTES
            Route::post('/employee/transfer/attachment/delete', [EmployeeTransferController::class, 'deleteAttachment'])->name('recruitment.employee.transfer.transfer.attachment.delete');
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END TRANSFER EMPLOYEES ROUTES کارمندان تبدیلی
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */


            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START SERVICE EMPLOYEES ROUTES کارمندان خدمتی
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employees/service', [EmployeeServiceController::class, 'getServiceEmployees'])->name('recruitment.employees.service');
            Route::get('/employees/service/search', [EmployeeServiceController::class, 'searchServiceEmployees'])->name('recruitment.employees.service.search');
            Route::get('/employees/service/report/excel', [EmployeeServiceController::class, 'searchServiceEmployeesForExcel'])->name('recruitment.employees.service.report.excel');
            Route::get('/employee/services', [EmployeeServiceController::class, 'getEmployeeServices'])->name('recruitment.employee.service.services.get');
            Route::get('/employee/services/tab', [EmployeeServiceController::class, 'getEmployeeServicesTab'])->name('recruitment.employee.service.services.tab.get');
            Route::get('/employee/service', [EmployeeServiceController::class, 'getEmployeeService'])->name('recruitment.employee.service.service.get');
            Route::get('/employees/service/all/count', [EmployeeServiceController::class, 'getServiceCount'])->name('recruitment.employees.service.count');

            // 2. EDIT & UPDATE ROUTES
            Route::get('/employee/service/{id}/edit', [EmployeeServiceController::class, 'showEmployeeServiceEditForm'])->name('recruitment.employee.service.service.edit');
            Route::post('/employee/service/update', [EmployeeServiceController::class, 'update'])->name('recruitment.employee.service.service.update');
            Route::post('/employee/service/status/update', [EmployeeServiceController::class, 'updateStatus'])->name('recruitment.employee.service.service.status.update');

            // 3. CREATE & STORE ROUTES
            Route::post('/employee/service/store', [EmployeeServiceController::class, 'store'])->name('recruitment.employee.service.service.store');

            // 4. DELETE ROUTES
            Route::post('/employee/service/attachment/delete', [EmployeeServiceController::class, 'deleteAttachment'])->name('recruitment.employee.service.service.attachment.delete');
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END SERVICE EMPLOYEES ROUTES کارمندان خدمتی
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */


            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START CONTRACT EMPLOYEES ROUTES کارمندان قرار دادی
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employees/contract', [EmployeeContractController::class, 'getContractEmployees'])->name('recruitment.employees.contract');
            Route::get('/employees/contract/search', [EmployeeContractController::class, 'searchContractEmployees'])->name('recruitment.employees.contract.search');
            Route::get('/employees/contract/report/excel', [EmployeeContractController::class, 'searchContractEmployeesForExcel'])->name('recruitment.employees.contract.report.excel');
            Route::get('/employee/contracts', [EmployeeContractController::class, 'getEmployeeContracts'])->name('recruitment.employee.contract.contracts.get');
            Route::get('/employee/contracts/tab', [EmployeeContractController::class, 'getEmployeeContractsTab'])->name('recruitment.employee.contract.contracts.tab.get');
            Route::get('/employee/contract', [EmployeeContractController::class, 'getEmployeeContractById'])->name('recruitment.employee.contract.contract.get');
            Route::get('/employees/contract/all/count', [EmployeeContractController::class, 'getContractorsCount'])->name('recruitment.employees.contract.count');

            // 2. EDIT & UPDATE ROUTES
            Route::get('/employee/contract/{id}/edit', [EmployeeContractController::class, 'showEmployeeContractEditForm'])->name('recruitment.employee.contract.contract.edit');
            Route::post('/employee/contract/update', [EmployeeContractController::class, 'update'])->name('recruitment.employee.contract.contract.update');
            Route::post('/employee/contract/status/update', [EmployeeContractController::class, 'updateStatus'])->name('recruitment.employee.contract.contract.status.update');

            // 3. CREATE & STORE ROUTES
            Route::post('/employee/contract/store', [EmployeeContractController::class, 'store'])->name('recruitment.employee.contract.contract.store');

            // 4. DELETE ROUTES
            Route::post('/employee/contract/attachment/delete', [EmployeeContractController::class, 'deleteAttachment'])->name('recruitment.employee.contract.contract.attachment.delete');
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END CONTRACT EMPLOYEES ROUTES کارمندان قرار دادی
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */


            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START EMPLOYEE CV ROUTES خلص سوانح کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employee/cv/report/excel', [EmployeeController::class, 'exportEmployeeTrainingsToExcel'])->name('recruitment.employee.cv.report.excel');
            Route::get('/employee/cv/print_preview/{employee_id}/{language}/{paper_tazkira}/{electronic_tazkira}', [EmployeeController::class, 'getCVPrintPreview'])->name('recruitment.employee.cv.print_preview');
            Route::get('/employee/cv/tab', [EmployeeController::class, 'getCVTab'])->name('recruitment.employee.cv.tab.get');
            Route::get('/employee/cv', [EmployeeController::class, 'getCV'])->name('recruitment.employee.cv.get');

            // 2. EDIT & UPDATE ROUTES

            // 3. CREATE & STORE ROUTES

            // 4. DELETE ROUTES

            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END EMPLOYEE CV ROUTES خلص سوانح کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */


            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START EMPLOYEE EXPERIENCE ROUTES تجارب کاری کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employee/experiences/report/excel', [EmployeeExperienceController::class, 'exportEmployeeExperiencesToExcel'])->name('recruitment.employee.experiences.report.excel');
            Route::get('/employee/experiences/tab', [EmployeeExperienceController::class, 'getEmployeeExperiencesTab'])->name('recruitment.employee.experiences.tab.get');
            Route::get('/employee/experiences', [EmployeeExperienceController::class, 'getEmployeeExperiences'])->name('recruitment.employee.experiences.get');
            Route::get('/employee/experience', [EmployeeExperienceController::class, 'getExperienceById'])->name('recruitment.employee.experience.get');

            // 2. EDIT & UPDATE ROUTES
            Route::get('/employee/experience/{id}/edit', [EmployeeExperienceController::class, 'showEmployeeExperienceEditForm'])->name('recruitment.employee.experience.edit');
            Route::post('/employee/experience/update', [EmployeeExperienceController::class, 'update'])->name('recruitment.employee.experience.update');
            Route::post('/employee/experience/status/update', [EmployeeExperienceController::class, 'updateStatus'])->name('recruitment.employee.experience.status.update');

            // 3. CREATE & STORE ROUTES
            Route::post('/employee/experience/store', [EmployeeExperienceController::class, 'store'])->name('recruitment.employee.experience.store');

            // 4. DELETE ROUTES
            Route::post('/employee/experience/delete', [EmployeeExperienceController::class, 'delete'])->name('recruitment.employee.experience.delete');
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END EMPLOYEE EXPERIENCE ROUTES تجارب کاری کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */



            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START EMPLOYEE PROMOTIONS ROUTES ترفیعات کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employee/promotions/report/excel', [EmployeePromotionController::class, 'exportEmployeePromotionsToExcel'])->name('recruitment.employee.promotions.report.excel');
            Route::get('/employee/promotions/tab', [EmployeePromotionController::class, 'getEmployeePromotionsTab'])->name('recruitment.employee.promotions.tab.get');
            Route::get('/employee/promotions', [EmployeePromotionController::class, 'getEmployeePromotions'])->name('recruitment.employee.promotions.get');
            Route::get('/employee/promotion', [EmployeePromotionController::class, 'getPromotionById'])->name('recruitment.employee.promotion.get');
            Route::get('/employees/promotion-ready', [EmployeePromotionController::class, 'getPromotionReadyEmployees'])->name('recruitment.employees.promotion.ready');
            Route::get('/employees/promotion-ready/search', [EmployeePromotionController::class, 'searchPromotionReadyEmployees'])->name('recruitment.employees.promotion.ready.search');
            Route::get('/employees/promotion-ready/all/count', [EmployeePromotionController::class, 'getPromotionReadyEmployeesCount'])->name('recruitment.employees.promotion.ready.count');
            Route::get('/employees/promotion-ready/report/excel', [EmployeePromotionController::class, 'searchPromotionReadyEmployeesForExcel'])->name('recruitment.employees.promotion.ready.report.excel');

            // 2. EDIT & UPDATE ROUTES
            Route::get('/employee/promotion/{id}/edit', [EmployeePromotionController::class, 'showEmployeePromotionEditForm'])->name('recruitment.employee.promotion.edit');
            Route::post('/employee/promotion/update', [EmployeePromotionController::class, 'update'])->name('recruitment.employee.promotion.update');

            // 3. CREATE & STORE ROUTES
            Route::post('/employee/promotion/store', [EmployeePromotionController::class, 'store'])->name('recruitment.employee.promotion.store');

            // 4. DELETE ROUTES
            Route::post('/employee/promotion/delete', [EmployeePromotionController::class, 'delete'])->name('recruitment.employee.promotion.delete');
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END EMPLOYEE PROMOTIONS ROUTES ترفیعات کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */



            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START EMPLOYEE EVALUATIONS ROUTES ارزیابی کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employee/evaluations/report/excel', [EmployeeEvaluationController::class, 'exportEmployeeEvaluationsToExcel'])->name('recruitment.employee.evaluations.report.excel');
            Route::get('/employee/evaluations/tab', [EmployeeEvaluationController::class, 'getEmployeeEvaluationsTab'])->name('recruitment.employee.evaluations.tab.get');
            Route::get('/employee/evaluations', [EmployeeEvaluationController::class, 'getEmployeeEvaluations'])->name('recruitment.employee.evaluations.get');
            Route::get('/employee/evaluation', [EmployeeEvaluationController::class, 'getEvaluationById'])->name('recruitment.employee.evaluation.get');

            // 2. EDIT & UPDATE ROUTES
            Route::get('/employee/evaluation/{id}/edit', [EmployeeEvaluationController::class, 'showEmployeeEvaluationEditForm'])->name('recruitment.employee.evaluation.edit');
            Route::post('/employee/evaluation/update', [EmployeeEvaluationController::class, 'update'])->name('recruitment.employee.evaluation.update');

            // 3. CREATE & STORE ROUTES
            Route::post('/employee/evaluation/store', [EmployeeEvaluationController::class, 'store'])->name('recruitment.employee.evaluation.store');

            // 4. DELETE ROUTES
            Route::post('/employee/evaluation/delete', [EmployeeEvaluationController::class, 'delete'])->name('recruitment.employee.evaluation.delete');
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END EMPLOYEE EVALUATIONS ROUTES ارزیابی کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */

            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START EMPLOYEE MAKAFATS ROUTES مکافات کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employee/makafats/report/excel', [EmployeeMakafatController::class, 'exportEmployeeMakafatsToExcel'])->name('recruitment.employee.makafats.report.excel');
            Route::get('/employee/makafats/tab', [EmployeeMakafatController::class, 'getEmployeeMakafatsTab'])->name('recruitment.employee.makafats.tab.get');
            Route::get('/employee/makafats', [EmployeeMakafatController::class, 'getEmployeeMakafats'])->name('recruitment.employee.makafats.get');
            Route::get('/employee/makafat', [EmployeeMakafatController::class, 'getMakafatById'])->name('recruitment.employee.makafat.get');

            // 2. EDIT & UPDATE ROUTES
            Route::get('/employee/makafat/{id}/edit', [EmployeeMakafatController::class, 'showEmployeeMakafatEditForm'])->name('recruitment.employee.makafat.edit');
            Route::post('/employee/makafat/update', [EmployeeMakafatController::class, 'update'])->name('recruitment.employee.makafat.update');

            // 3. CREATE & STORE ROUTES
            Route::post('/employee/makafat/store', [EmployeeMakafatController::class, 'store'])->name('recruitment.employee.makafat.store');

            // 4. DELETE ROUTES
            Route::post('/employee/makafat/delete', [EmployeeMakafatController::class, 'delete'])->name('recruitment.employee.makafat.delete');
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END EMPLOYEE MAKAFATS ROUTES مکافات کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */


            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START EMPLOYEE PUNISHMENTS ROUTES تأدیبات کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employee/punishments/report/excel', [EmployeePunishmentController::class, 'exportEmployeePunishmentsToExcel'])->name('recruitment.employee.punishments.report.excel');
            Route::get('/employee/punishments/tab', [EmployeePunishmentController::class, 'getEmployeePunishmentsTab'])->name('recruitment.employee.punishments.tab.get');
            Route::get('/employee/punishments', [EmployeePunishmentController::class, 'getEmployeePunishments'])->name('recruitment.employee.punishments.get');
            Route::get('/employee/punishment', [EmployeePunishmentController::class, 'getPunishmentById'])->name('recruitment.employee.punishment.get');

            // 2. EDIT & UPDATE ROUTES
            Route::get('/employee/punishment/{id}/edit', [EmployeePunishmentController::class, 'showEmployeePunishmentEditForm'])->name('recruitment.employee.punishment.edit');
            Route::post('/employee/punishment/update', [EmployeePunishmentController::class, 'update'])->name('recruitment.employee.punishment.update');

            // 3. CREATE & STORE ROUTES
            Route::post('/employee/punishment/store', [EmployeePunishmentController::class, 'store'])->name('recruitment.employee.punishment.store');

            // 4. DELETE ROUTES
            Route::post('/employee/punishment/delete', [EmployeePunishmentController::class, 'delete'])->name('recruitment.employee.punishment.delete');
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END EMPLOYEE PUNISHMENTS ROUTES تأدیبات کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */


            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START EMPLOYEE TRAININGS ROUTES آموزش های کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employee/trainings/report/excel', [EmployeeTrainingController::class, 'exportEmployeeTrainingsToExcel'])->name('recruitment.employee.trainings.report.excel');
            Route::get('/employee/trainings/tab', [EmployeeTrainingController::class, 'getEmployeeTrainingsTab'])->name('recruitment.employee.trainings.tab.get');
            Route::get('/employee/trainings', [EmployeeTrainingController::class, 'getEmployeeTrainings'])->name('recruitment.employee.trainings.get');
            Route::get('/employee/training', [EmployeeTrainingController::class, 'getTrainingById'])->name('recruitment.employee.training.get');

            // 2. EDIT & UPDATE ROUTES
            Route::get('/employee/training/{id}/edit', [EmployeeTrainingController::class, 'showEmployeeTrainingEditForm'])->name('recruitment.employee.training.edit');
            Route::post('/employee/training/update', [EmployeeTrainingController::class, 'update'])->name('recruitment.employee.training.update');

            // 3. CREATE & STORE ROUTES
            Route::post('/employee/training/store', [EmployeeTrainingController::class, 'store'])->name('recruitment.employee.training.store');

            // 4. DELETE ROUTES
            Route::post('/employee/training/delete', [EmployeeTrainingController::class, 'delete'])->name('recruitment.employee.training.delete');
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END EMPLOYEE TRAININGS ROUTES آموزش های کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */


            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START EMPLOYEE ASKARIES ROUTES دوره های عسکری کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employee/askaries/report/excel', [EmployeeAskariController::class, 'exportEmployeeAskariesToExcel'])->name('recruitment.employee.askaries.report.excel');
            Route::get('/employee/askaries/tab', [EmployeeAskariController::class, 'getEmployeeAskariesTab'])->name('recruitment.employee.askaries.tab.get');
            Route::get('/employee/askaries', [EmployeeAskariController::class, 'getEmployeeAskaries'])->name('recruitment.employee.askaries.get');
            Route::get('/employee/askari', [EmployeeAskariController::class, 'getAskariById'])->name('recruitment.employee.askari.get');

            // 2. EDIT & UPDATE ROUTES
            Route::get('/employee/askari/{id}/edit', [EmployeeAskariController::class, 'showEmployeeAskariEditForm'])->name('recruitment.employee.askari.edit');
            Route::post('/employee/askari/update', [EmployeeAskariController::class, 'update'])->name('recruitment.employee.askari.update');

            // 3. CREATE & STORE ROUTES
            Route::post('/employee/askari/store', [EmployeeAskariController::class, 'store'])->name('recruitment.employee.askari.store');

            // 4. DELETE ROUTES
            Route::post('/employee/askari/delete', [EmployeeAskariController::class, 'delete'])->name('recruitment.employee.askari.delete');
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END EMPLOYEE ASKARIES ROUTES دوره های عسکری کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */



            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START EMPLOYEE FIRE ROUTES کارمندان منفکی و منفکی کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employees/fired', [EmployeeFireController::class, 'getFiredEmployees'])->name('recruitment.employees.fired');
            Route::get('/employees/fired/search', [EmployeeFireController::class, 'searchFiredEmployees'])->name('recruitment.employees.fired.search');
            Route::get('/employees/fired/report/excel', [EmployeeFireController::class, 'searchFiredEmployeesForExcel'])->name('recruitment.employees.fired.report.excel');
            Route::get('/employees/fired/all/count', [EmployeeFireController::class, 'getFiredEmployeesCount'])->name('recruitment.employees.fired.count');
            Route::get('/employee/fired/tab', [EmployeeFireController::class, 'getEmployeeFireTab'])->name('recruitment.employee.fired.tab');

            // 2. EDIT & UPDATE ROUTES
            Route::post('/employee/fire/update', [EmployeeFireController::class, 'update'])->name('recruitment.employee.fire.update');

            // 3. CREATE & STORE ROUTES
            Route::post('/employee/fire', [EmployeeFireController::class, 'store'])->name('recruitment.employee.fire');

            // 4. DELETE ROUTES
            Route::post('/employee/fire/attachment/delete', [EmployeeFireController::class, 'deleteAttachment'])->name('recruitment.employee.fire.attachment.delete');

            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END EMPLOYEE FIRE ROUTES کارمندان منفکی و منفکی کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */



            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START EMPLOYEE RESIGN ROUTES کارمندان مستعفی و استعفأ کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employees/resigned', [EmployeeResignController::class, 'getResignedEmployees'])->name('recruitment.employees.resigned');
            Route::get('/employees/resigned/search', [EmployeeResignController::class, 'searchResignedEmployees'])->name('recruitment.employees.resigned.search');
            Route::get('/employees/resigned/report/excel', [EmployeeResignController::class, 'searchResignedEmployeesForExcel'])->name('recruitment.employees.resigned.report.excel');
            Route::get('/employees/resigned/all/count', [EmployeeResignController::class, 'getResignedEmployeesCount'])->name('recruitment.employees.resigned.count');
            Route::get('/employee/resigned/tab', [EmployeeResignController::class, 'getEmployeeResignTab'])->name('recruitment.employee.resigned.tab');

            // 2. EDIT & UPDATE ROUTES
            Route::post('/employee/resign/update', [EmployeeResignController::class, 'update'])->name('recruitment.employee.resign.update');

            // 3. CREATE & STORE ROUTES
            Route::post('/employee/resign', [EmployeeResignController::class, 'store'])->name('recruitment.employee.resign');

            // 4. DELETE ROUTES
            Route::post('/employee/resign/attachment/delete', [EmployeeResignController::class, 'deleteAttachment'])->name('recruitment.employee.resign.attachment.delete');

            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END EMPLOYEE RESIGN ROUTES کارمندان مستعفی و استعفأ کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START EMPLOYEE RETIRE ROUTES کارمندان متقاعد و تقاعد کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employees/retired', [EmployeeRetireController::class, 'getRetiredEmployees'])->name('recruitment.employees.retired');
            Route::get('/employees/retired/search', [EmployeeRetireController::class, 'searchRetiredEmployees'])->name('recruitment.employees.retired.search');
            Route::get('/employees/retired/report/excel', [EmployeeRetireController::class, 'searchRetiredEmployeesForExcel'])->name('recruitment.employees.retired.report.excel');
            Route::get('/employees/retired/all/count', [EmployeeRetireController::class, 'getRetiredEmployeesCount'])->name('recruitment.employees.retired.count');
            Route::get('/employee/retired/tab', [EmployeeRetireController::class, 'getEmployeeRetireTab'])->name('recruitment.employee.retired.tab');
            Route::get('/employees/retire-ready', [EmployeeRetireController::class, 'getRetireReadyEmployees'])->name('recruitment.employees.retire.ready');
            Route::get('/employees/retire-ready/seach', [EmployeeRetireController::class, 'searchRetireReadyEmployees'])->name('recruitement.employees.retire.ready.search');
            Route::get('/employees/retire-ready/report/excel', [EmployeeRetireController::class, 'searchRetireReadyEmployeesForExcel'])->name('recruitment.employees.retire.ready.report.excel');
            Route::get('/employees/retire-ready/all/count', [EmployeeRetireController::class, 'getRetireReadyEmployeesCount'])->name('recruitment.employees.retire.ready.count');

            // 2. EDIT & UPDATE ROUTES
            Route::post('/employee/retire/update', [EmployeeRetireController::class, 'update'])->name('recruitment.employee.retire.update');

            // 3. CREATE & STORE ROUTES
            Route::post('/employee/retire', [EmployeeRetireController::class, 'store'])->name('recruitment.employee.retire');

            // 4. DELETE ROUTES
            Route::post('/employee/retire/attachment/delete', [EmployeeRetireController::class, 'deleteAttachment'])->name('recruitment.employee.retire.attachment.delete');

            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END EMPLOYEE RETIRE ROUTES کارمندان متقاعد و تقاعد کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */


            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START TANQIS EMPLOYEES ROUTES کارمندان تنقیص شده و تنقیص کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employees/tanqis', [EmployeeTanqisController::class, 'getTanqisEmployees'])->name('recruitment.employees.tanqis');
            Route::get('/employees/tanqis/search', [EmployeeTanqisController::class, 'searchTanqisEmployees'])->name('recruitment.employees.tanqis.search');
            Route::get('/employees/tanqis/report/excel', [EmployeeTanqisController::class, 'searchTanqisEmployeesForExcel'])->name('recruitment.employees.tanqis.report.excel');
            Route::get('/employees/tanqis/all/count', [EmployeeTanqisController::class, 'getTanqisEmployeesCount'])->name('recruitment.employees.tanqis.count');
            Route::post('/employee/tanqis', [EmployeeTanqisController::class, 'store'])->name('recruitment.employee.tanqis');
            Route::get('/employee/tanqis/tab', [EmployeeTanqisController::class, 'getEmployeeTanqisTab'])->name('recruitment.employee.tanqis.tab');

            // 2. EDIT & UPDATE ROUTES
            Route::post('/employee/tanqis/update', [EmployeeTanqisController::class, 'update'])->name('recruitment.employee.tanqis.update');

            // 3. CREATE & STORE ROUTES

            // 4. DELETE ROUTES
            Route::post('/employee/tanqis/attachment/delete', [EmployeeTanqisController::class, 'deleteAttachment'])->name('recruitment.employee.tanqis.attachment.delete');

            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END TANQIS EMPLOYEES ROUTES کارمندان تنقیص شده و تنقیص کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */




            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START EMPLOYEE GUARANTEE ROUTES کارمندان دارای تضمینات و تضمینات کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employees/guarantee', [EmployeeGuaranteeController::class, 'getGuaranteedEmployees'])->name('recruitment.employees.guarantee');
            Route::get('/employees/guarantee/search', [EmployeeGuaranteeController::class, 'searchGuaranteedEmployees'])->name('recruitment.employees.guarantee.search');
            Route::get('/employees/guarantee/report/excel', [EmployeeGuaranteeController::class, 'searchGuaranteedEmployeesForExcel'])->name('recruitment.employees.guarantee.report.excel');
            Route::get('/employee/guarantees/report/excel', [EmployeeGuaranteeController::class, 'searchGuaranteedEmployeesForExcel'])->name('recruitment.employee.guarantees.report.excel');
            Route::get('/employee/guarantees/tab', [EmployeeGuaranteeController::class, 'getEmployeeGuaranteesTab'])->name('recruitment.employee.guarantee.guarantees.tab.get');
            Route::get('/employee/guarantees', [EmployeeGuaranteeController::class, 'getEmployeeGuarantees'])->name('recruitment.employee.guarantee.guarantees.get');
            Route::get('/employee/guarantee', [EmployeeGuaranteeController::class, 'getEmployeeGuarantee'])->name('recruitment.employee.guarantee.guarantee.get');
            Route::get('/employees/guarantee/all/count', [EmployeeGuaranteeController::class, 'getGuaranteedEmployeesCount'])->name('recruitment.employees.guarantee.count');

            // 2. EDIT & UPDATE ROUTES
            Route::get('/employee/guarantee/{guarantee_id}/{guarantee_type}edit', [EmployeeGuaranteeController::class, 'showEmployeeGuaranteeEditForm'])->name('recruitment.employee.guarantee.guarantee.edit');
            Route::post('/employee/guarantee/cash/update', [EmployeeGuaranteeController::class, 'updateCash'])->name('recruitment.employee.guarantee.guarantee.cash.update');
            Route::post('/employee/guarantee/property/update', [EmployeeGuaranteeController::class, 'updateProperty'])->name('recruitment.employee.guarantee.guarantee.property.update');

            // 3. CREATE & STORE ROUTES
            Route::post('/employee/guarantee/cash/store', [EmployeeGuaranteeController::class, 'storeCash'])->name('recruitment.employee.guarantee.guarantee.cash.store');
            Route::post('/employee/guarantee/property/store', [EmployeeGuaranteeController::class, 'storeProperty'])->name('recruitment.employee.guarantee.guarantee.property.store');

            // 4. DELETE ROUTES
            Route::post('/employee/guarantees/delete', [EmployeeGuaranteeController::class, 'deleteAllGuarantees'])->name('recruitment.employee.guarantee.guarantees.all.delete');
            Route::post('/employee/guarantee/cash/delete', [EmployeeGuaranteeController::class, 'deleteCash'])->name('recruitment.employee.guarantee.guarantee.cash.delete');

            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END EMPLOYEE GUARANTEE ROUTES کارمندان دارای تضمینات و تضمینات کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */



            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START EMPLOYEE ATTACHMENTS ROUTES اسناد کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employee/attachments/tab', [EmployeeAttachmentController::class, 'getAttachmentsTab'])->name('recruitment.employee.attachments.tab.get');
            Route::get('/employee/attachments', [EmployeeAttachmentController::class, 'getAttachments'])->name('recruitment.employee.attachments.get');
            Route::get('/employees/attachments/all/count', [EmployeeAttachmentController::class, 'getAttachmentsCount'])->name('recruitment.employee.attachments.all.count');

            // 2. EDIT & UPDATE ROUTES

            // 3. CREATE & STORE ROUTES
            Route::post('/employee/attachment/upload', [EmployeeAttachmentController::class, 'upload'])->name('recruitment.employee.attachment.upload');

            // 4. DELETE ROUTES
            Route::post('/employee/attachment/delete', [EmployeeAttachmentController::class, 'delete'])->name('recruitment.employee.attachment.delete');
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END EMPLOYEE ATTACHMENTS ROUTES اسناد کارمند
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */



            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START EMPLOYEES WITH TEMPORARY EDUCATIONAL DOCUMENTS ROUTES کارمندان دارای اسناد تحصیلی مؤقت
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/employees/education/temporary', [TemporaryEducationDocumentController::class, 'getEmployeesWithTemporaryEducationDocuments'])->name('recruitment.employees.education.temporary');
            Route::get('/employees/education/temporary/search', [TemporaryEducationDocumentController::class, 'searchEmployeesWithTemporaryEducationDocuments'])->name('recruitment.employees.education.temporary.search');
            Route::get('/employees/education/temporary/report/excel', [TemporaryEducationDocumentController::class, 'exportEmployeeWithTemporaryEducationDocumentsToExcel'])->name('recruitment.employees.education.temporary.report.excel');
            Route::get('/employees/education/temporary/all/count', [TemporaryEducationDocumentController::class, 'getEmployeeWithTemporaryEducationDocumentsCount'])->name('recruitment.employees.education.temporary.count');

            // 2. EDIT & UPDATE ROUTES

            // 3. CREATE & STORE ROUTES

            // 4. DELETE ROUTES

            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END EMPLOYEES WITH TEMPORARY EDUCATIONAL DOCUMENTS ROUTES کارمندان دارای اسناد تحصیلی مؤقت
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */



            // EZAFA BAST EMPLOYEES ROUTES اضافه بست
            Route::get('/ezafa-bast', [EmployeeController::class, 'getBarhalEmployees'])->name('recruitment.ezafa-bast');

            // EMPLOYEE GUARANTEES ROUTES تضمینات کارمندان (معتمد، دریور، خانه سامان)
            Route::get('/guarantees', [EmployeeController::class, 'getBarhalEmployees'])->name('recruitment.guarantees');

            // EMPLOYEES WITH TEMPORARY EDUCATIONAL DOCUMENTS ROUTES کارمندان دارای اسناد تحصیلی مؤقت یا تائیدی
            Route::get('/temporary-education-documents', [EmployeeController::class, 'getBarhalEmployees'])->name('recruitment.temporary-education-documents');

            // GENERAL ROUTES
            Route::get('employee-account-status', [EmployeeController::class, 'getEmployeeAccountStatus'])->name('employee.account.status');
            Route::get('department-employees', [EmployeeController::class, 'getEmployeesByDepartmentId'])->name('employees.department.rendered.get');
            Route::get('department-employees-ordered', [EmployeeController::class, 'getEmployeesByDepartmentIdForAttendanceOrdered'])->name('employees.department.rendered.ordered.get');
      });
      /*
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      | END RECRUITMENT ROUTES استخدام
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      */



      /*
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      | START CAPACITY BUILDING ROUTES ارتقای ظرفیت
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      */
      Route::prefix('capacity-building')->group(function () {
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/trainings', [TrainingController::class, 'getTrainings'])->name('capacity-building.trainings');
            Route::get('/trainings/search', [TrainingController::class, 'searchTrainings'])->name('capacity-building.trainings.search');
            Route::get('/trainings/report/excel', [TrainingController::class, 'searchTrainingsForExcel'])->name('capacity-building.trainings.report.excel');
            Route::get('/trainings/all/count', [TrainingController::class, 'getTrainingsCount'])->name('capacity-building.trainings.count');
            Route::get('/employees/department/rendered', [EmployeeController::class, 'getEmployeesForTrainingSelectionByDepartmentId'])->name('capacity-building.employees.rendered');
            Route::get('/trainers', [TrainerController::class, 'getTrainers'])->name('capacity-building.trainers');
            Route::get('/trainers/search', [TrainerController::class, 'searchTrainers'])->name('capacity-building.trainers.search');
            Route::get('/trainers/report/excel', [TrainerController::class, 'searchTrainersForExcel'])->name('capacity-building.trainers.report.excel');
            Route::get('/trainers/all/count', [TrainerController::class, 'getTrainersCount'])->name('capacity-building.trainers.count');

            // 2. EDIT & UPDATE ROUTES
            Route::get('/training/{id}/edit', [TrainingController::class, 'showEditForm'])->name('capacity-building.training.edit');
            Route::put('/training/{id}/update', [TrainingController::class, 'update'])->name('capacity-building.training.update');
            Route::get('/trainer/{id}/edit', [TrainerController::class, 'showEditForm'])->name('capacity-building.trainer.edit');
            Route::put('/trainer/{id}/update', [TrainerController::class, 'update'])->name('capacity-building.trainer.update');

            // 3. CREATE & STORE ROUTES
            Route::get('/training/create', [TrainingController::class, 'showCreateForm'])->name('capacity-building.training.create');
            Route::post('/training/store', [TrainingController::class, 'store'])->name('capacity-building.training.store');
            Route::get('/trainer/create', [TrainerController::class, 'showCreateForm'])->name('capacity-building.trainer.create');
            Route::post('/trainer/store', [TrainerController::class, 'store'])->name('capacity-building.trainer.store');

            // 4. DELETE ROUTES
            Route::delete('/training/{id}/attachment/delete', [TrainingController::class, 'deleteAttachment'])->name('capacity-building.training.attachment.delete');
            Route::delete('/training/{id}/delete', [TrainingController::class, 'delete'])->name('capacity-building.training.delete');
      });
      /*
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      | END CAPACITY BUILDING ROUTES ارتقای ظرفیت
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      */



      /*
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      | START CARDS ROUTES کارت ها
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      */
      Route::prefix('cards')->name('cards.')->group(function () {
            Route::get('card-index', [CardController::class, 'index'])->name('index');
            Route::get('card-print-preview', [CardController::class, 'cardPreview'])->name('card.print.preview');
            Route::get('card-print/{employee_id?}/{type_id?}/{is_secondary?}', [CardController::class, 'cardPrint'])->name('card.print');
            Route::get('card-print-test/{employee_id}/{type_id}/{is_secondary?}', [CardController::class, 'cardPrintTest'])->name('card.print.test');
            Route::get('card-search', [CardController::class, 'search'])->name('search');
            Route::get('card-search-emp', [CardController::class, 'searchPerEmployeeCard'])->name('search.emp');
            Route::get('card-setup/{id}', [CardController::class, 'cardSetup'])->name('card.setup');
            Route::get('set-emp-card/{id}/{period?}', [CardController::class, 'setEmpCard'])->name('set.emp.card');
            Route::post('save-emp-card/{id}', [CardController::class, 'saveEmpCard'])->name('save.emp.card');
            Route::post('card-unlock', [CardController::class, 'cardUnlock'])->name('card.unlock');
            Route::get('cards/report/excel', [CardController::class, 'searchCardsForExcel'])->name('cards.report.excel');
            Route::get('printed-cards', [CardController::class, 'getAllPrintedCards'])->name('printed.cards');
            Route::get('printed-cards/search', [CardController::class, 'printedCardsSearch'])->name('printed.cards.search');
            Route::get('printed-cards/report/excel', [CardController::class, 'searchPrintedCardsForExcel'])->name('printed.cards.report.excel');
      });
      /*
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      | END CARDS ROUTES کارت ها
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      */



      /*
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      | START TASHKILAT ROUTES تشکیلات
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      */
      Route::prefix('tashkil')->group(function () {
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START BASTS ROUTES
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            // 1. DATA RETRIEVAL ROUTES
            Route::get('/home', [TashkilatController::class, 'getTashkilat'])->name('tashkil.tashkilat');
            Route::get('/search', [TashkilatController::class, 'searchTashkilat'])->name('tashkil.search');
            Route::get('/search/report/excel', [TashkilatController::class, 'exportTashkilatToExcel'])->name('tashkil.search.report.excel');
            Route::get('/employee/name', [TashkilatController::class, 'getEmployeeNameByTashkilId'])->name('tashkil.employee.name');
            Route::get('/change-history', [TashkilatController::class, 'getTashkilChangeHistory'])->name('tashkil.change-history');
            Route::get('/basts', [TashkilatController::class, 'getTashkilatByDepartmentId'])->name('tashkil.department.basts.rendered');
            Route::get('/basts/free', [TashkilatController::class, 'getFreeBastsByDepartmentId'])->name('tashkil.department.basts.free.rendered');
            Route::get('/bast', [TashkilatController::class, 'getTashkilBastById'])->name('tashkil.bast');
            Route::get('/bast/count', [TashkilatController::class, 'getTashkilatCountByBast'])->name('tashkil.count.bast');
            Route::get('/copy/basts', [TashkilatController::class, 'copyTashkil'])->name('tashkil.copy.basts'); //
            Route::get('copy/getDepartmentsRendered', [TashkilatController::class, 'getUserDepartmentsRendered'])->name('tashkil.departments.rendered');
            Route::get('/copy/list/basts', [TashkilatController::class, 'listTashkilBasts'])->name('tashkil.copy.list.basts'); //
            Route::post('/copy/create/basts', [TashkilatController::class, 'createTashkilBasts'])->name('tashkil.copy.create.basts'); //



            // 2. EDIT & UPDATE ROUTES            
            Route::get('/{id}/edit', [TashkilatController::class, 'showUpdateForm'])->name('tashkil.edit');
            Route::put('/{id}/update', [TashkilatController::class, 'update'])->name('tashkil.update');

            // 3. CREATE, STORE & REPLICATE ROUTES
            Route::get('/create', [TashkilatController::class, 'showCreateForm'])->name('tashkil.create');
            Route::post('/store', [TashkilatController::class, 'store'])->name('tashkil.store');
            Route::post('/bast/replicate', [TashkilatController::class, 'replicateBast'])->name('tashkil.replicate');

            // 4. DELETE ROUTES
            Route::delete('/delete', [TashkilatController::class, 'destroy'])->name('tashkil.delete');
            Route::delete('/job-description/delete', [TashkilatController::class, 'deleteJobDescription'])->name('tashkil.job-description.delete');
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END BASTS ROUTES
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */


            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START DEPARTMENTS ROUTES
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            Route::prefix('department')->group(function () {
                  Route::get('list', [DepartmentController::class, 'index'])->name('departments.home');
                  Route::get('add', [DepartmentController::class, 'add'])->name('departments.add');
                  Route::post('store', [DepartmentController::class, 'store'])->name('departments.store');
                  Route::post('update/{id}', [DepartmentController::class, 'update'])->name('departments.update');
                  Route::get('edit/{id?}', [DepartmentController::class, 'edit'])->name('departments.edit');
                  Route::get('edit-direct/{id?}', [DepartmentController::class, 'editDirect'])->name('departments.edit.direct');
                  Route::get('search', [DepartmentController::class, 'search'])->name('departments.search');
                  Route::get('/{id}/sub', [DepartmentController::class, 'getDepartment'])->name('departments.sub');
                  Route::post('setStatus', [DepartmentController::class, 'setStatus'])->name('departments.setStatus');
                  Route::get('getDepartment', [DepartmentController::class, 'getDepartment'])->name('departments.get');
                  Route::get('getDepartmentsRendered', [DepartmentController::class, 'getUserDepartmentsRendered'])->name('departments.rendered.get');
                  Route::get('department-employee', [DepartmentController::class, 'getDepartmentEmployeesBrief'])->name('department.employee');
                  Route::get('/all-rendered', [DepartmentController::class, 'getAllDepartmentsRendered'])->name('departments.get-all.rendered');
                  Route::get('/chart-data', [DepartmentController::class, 'getDepartmentsChartData'])->name('departments.chart.get');
                  Route::get('/chart', [DepartmentController::class, 'getDepartmentsChart'])->name('departments.chart');
            });
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END DEPARTMENTS ROUTES
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */

            Route::prefix('report')->group(function () {
                  Route::get('tashkilat', [TashkilatController::class, 'getTashkilatReport'])->name('tashkil.report.tashkilat');
                  Route::get('tashkilat/tashkilat-statistic/excel', [TashkilatController::class, 'getTashkilatStatisticExcelReport'])->name('tashkil.tashkil-statistic.report.excel');
            });

            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START NTA TASHKILAT ROUTES
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            Route::prefix('nta')->group(function () {
                  Route::get('/home', [NtaTashkilatController::class, 'getNtaTashkilat'])->name('tashkil.nta.tashkilat');
                  Route::get('/create', [NtaTashkilatController::class, 'showCreateForm'])->name('tashkil.nta.create');
                  Route::post('/store', [NtaTashkilatController::class, 'store'])->name('tashkil.nta.store');
                  Route::get('/{id}/edit', [NtaTashkilatController::class, 'showEditForm'])->name('tashkil.nta.edit');
                  Route::put('/{id}/update', [NtaTashkilatController::class, 'update'])->name('tashkil.nta.update');
                  Route::delete('/delete', [NtaTashkilatController::class, 'destroy'])->name('tashkil.nta.delete');
                  Route::get('search', [NtaTashkilatController::class, 'searchNtaTashkilat'])->name('tashkil.nta.search');
                  Route::get('search/report/excel', [NtaTashkilatController::class, 'exportNtaTashkilatToExcel'])->name('nta.tashkil.search.report.excel');
                  Route::delete('/job-description/delete', [NtaTashkilatController::class, 'deleteJobDescription'])->name('nta.tashkil.job-description.delete');
                  Route::post('/bast/replicate', [NtaTashkilatController::class, 'replicateBast'])->name('nta.tashkil.replicate');
                  Route::get('/nta/basts/free', [NtaTashkilatController::class, 'getFreeNtaBastsByDepartmentId'])->name('nta.tashkil.department.basts.free.rendered');
                  Route::get('/nta/bast', [NtaTashkilatController::class, 'getNtaTashkilBastById'])->name('nta.tashkil.bast');
            });

            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END NTA TASHKILAT ROUTES
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */

            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START BILMAQTA TASHKILAT ROUTES
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            Route::prefix('bilmaqta')->group(function () {
                  Route::get('/home', [BilmaqtaTashkilatController::class, 'getBilmaqtaEmployeeForTashkilat'])->name('tashkil.bilmaqta.tashkilat');
                  Route::get('{id}/edit', [BilmaqtaTashkilatController::class, 'showEditForm'])->name('tashkil.bilmaqta.edit');
                  Route::put('/{id}/update', [BilmaqtaTashkilatController::class, 'update'])->name('tashkil.bilmaqta.update');
                  Route::delete('/job-description/delete', [BilmaqtaTashkilatController::class, 'deleteBilmaqtaJobDescription'])->name('tashkil.bilmaqta.job-description.delete');
                  Route::get('/search', [BilmaqtaTashkilatController::class, 'searchBilmaqtaTashkilat'])->name('tashkil.bilmaqta.search');
            });

            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END BILMAQTA TASHKILAT ROUTES
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */

              /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START JOBDESCRIPTION TASHKILAT ROUTES
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            Route::prefix('jdescription')->group(function () {
                  Route::get('/home', [BilmaqtaTashkilatController::class, 'getBilmaqtaEmployeeForTashkilat'])->name('tashkil.bilmaqta.tashkilat');
                
            });

            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END JOBDESCRIPTION TASHKILAT ROUTES
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
      });
      /*
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      | END TASHKILAT ROUTES تشکیلات
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      */



      /*
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      | START ATTENDANCE MODULE ROUTES حاضری
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      */
      Route::prefix('attendance')->group(function () {
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START ATTENDANCE ROUTES حاضری
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            Route::prefix('attendance')->group(function () {
                  // 1. DATA RETRIEVAL ROUTES
                  Route::get('/employees',  [AttendanceController::class, 'getEmployees'])->name('attendance.employees');
                  Route::get('/employees/search',  [AttendanceController::class, 'searchEmployees'])->name('attendance.employees.search');
                  Route::get('/employee/{year}/{month}/{employee_id}/attendance/{tab}',  [AttendanceController::class, 'showEmployeeAttendace'])->name('attendance.employee.attendance');
                  Route::get('/employee/next/{year}/{month}/{employee_id}/attendance',  [AttendanceController::class, 'showNextEmployeeAttendace'])->name('attendance.employee.next.attendance');
                  Route::get('/employee/previous/{year}/{month}/{employee_id}/attendance',  [AttendanceController::class, 'showPreviousEmployeeAttendace'])->name('attendance.employee.previous.attendance');
                  Route::get('/leaves/extra-entertainment', [LeaveController::class, 'searchExtraEntertainmentLeaves'])->name('attendance.leaves.extra-entertainment.get');
                  Route::get('/leave/extra-entertainment', [LeaveController::class, 'getExtraEntertainmentLeaveById'])->name('attendance.leave.extra-entertainment.get');
                  Route::get('/leaves/report/excel', [LeaveController::class, 'exportLeavesToExcel'])->name('attendance.leaves.report.excel');
                  Route::get('/leaves', [LeaveController::class, 'searchLeaves'])->name('attendance.leaves.get');
                  Route::get('/leave', [LeaveController::class, 'getLeaveById'])->name('attendance.leave.get');
                  Route::get('/in-out-count',  [AttendanceController::class, 'getAttendanceInOutCount'])->name('attendance.employees.in-out-count');

                  // 2. EDIT & UPDATE ROUTES
                  Route::get('/leave/extra-entertainment/delete', [LeaveController::class, 'deleteExtraEntertainmentLeave'])->name('attendance.leave.extra-entertainment.delete');
                  Route::post('/leave/extra-entertainment/update', [LeaveController::class, 'updateExtraEntertainmentLeave'])->name('attendance.leave.extra-entertainment.update');
                  Route::get('/leave/{id}/edit', [LeaveController::class, 'showLeaveEditForm'])->name('attendance.leave.edit');
                  Route::post('/leave/update', [LeaveController::class, 'update'])->name('attendance.leave.update');
                  Route::post('/image/reject', [AttendanceController::class, 'rejectImage'])->name('attendance.image.reject');

                  // 3. CREATE & STORE ROUTES
                  Route::get('/leave/create', [LeaveController::class, 'showLeaveCreateForm'])->name('attendance.leave.create');
                  Route::post('/leave/extra-entertainment/store', [LeaveController::class, 'storeExtraEntertainmentLeave'])->name('attendance.leave.extra-entertainment.store');
                  Route::post('/leave/store', [LeaveController::class, 'store'])->name('attendance.leave.store');

                  // 4. DELETE ROUTES
                  Route::post('/leave/delete', [LeaveController::class, 'delete'])->name('attendance.leave.delete');

                  Route::get('/employee/taken/leave/create', [LeaveController::class, 'showTakenLeaveCreateForm'])->name('attendance.employee.taken.leave.create');
                  Route::post('/employee/taken/leave/store', [LeaveController::class, 'storeEmployeeTakenLeave'])->name('attendance.employee.taken.leave.store');
                  Route::get('/employee/taken/leave/{id}/edit', [LeaveController::class, 'showEmployeeTakenLeaveEditForm'])->name('attendance.employee.taken.leave.edit');
                  Route::post('/employee/taken/leave/update', [LeaveController::class, 'updateEmployeeTakenLeave'])->name('attendance.employee.taken.leave.update');
                  Route::get('/employee/taken/leaves', [LeaveController::class, 'searchEmployeeTakenLeave'])->name('attendance.employee.taken.leaves.get');
                  Route::post('employee/taken/leave/delete', [LeaveController::class, 'deleteEmployeeTakenLeave'])->name('attendance.employee.takenleave.delete');
            });
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END ATTENDANCE ROUTES حاضری 
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */


            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START REPORTS ROUTES راپور های حاضری
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            Route::prefix('reports')->group(function () {
                  Route::get('/annual-leaves',  [AttendanceReportController::class, 'getEmployeesAnnualLeavesReport'])->name('attendance.reports.annual-leaves');
                  Route::get('/annual-leaves/search',  [AttendanceReportController::class, 'searchEmployeesForAnnualLeavesReport'])->name('attendance.reports.annual-leaves.search');
                  Route::get('/annual-leaves/excel',  [AttendanceReportController::class, 'getEmployeesAnnualLeavesExcelReport'])->name('attendance.reports.annual-leaves.report.excel');

                  Route::get('/attendance',  [AttendanceReportController::class, 'getEmployeesAttendanceReport'])->name('attendance.reports.attendance');
                  Route::get('/attendance/search',  [AttendanceReportController::class, 'searchEmployeesForAttendanceReport'])->name('attendance.reports.attendance.search');
                  Route::get('/attendance/excel',  [AttendanceReportController::class, 'getEmployeesAttendanceExcelReport'])->name('attendance.reports.attendance.report.excel');

                  Route::get('/daily-attendance',  [AttendanceReportController::class, 'getEmployeesDailyAttendanceReport'])->name('attendance.reports.daily-attendance');
                  Route::get('/daily-attendance/search',  [AttendanceReportController::class, 'searchEmployeesForDailyAttendanceReport'])->name('attendance.reports.daily-attendance.search');
                  Route::get('/daily-attendance/excel',  [AttendanceReportController::class, 'getEmployeesDailyAttendanceExcelReport'])->name('attendance.reports.daily-attendance.report.excel');

                  Route::get('/time-based-attendance',  [AttendanceReportController::class, 'getEmployeesTimeBasedAttendanceReport'])->name('attendance.reports.time-based-attendance');
                  Route::get('/time-based-attendance/search',  [AttendanceReportController::class, 'searchEmployeesForTimeBasedAttendanceReport'])->name('attendance.reports.time-based-attendance.search');
                  Route::get('/time-based-attendance/excel',  [AttendanceReportController::class, 'getEmployeesTimeBasedAttendanceExcelReport'])->name('attendance.reports.time-based-attendance.report.excel');

                  Route::get('/e-attendance-employees',  [AttendanceReportController::class, 'getEAttendanceEmployeesReport'])->name('attendance.reports.e-attendance-employees');
                  Route::get('/e-attendance-employees/search',  [AttendanceReportController::class, 'searchEAttendanceEmployeesReport'])->name('attendance.reports.e-attendance-employees.search');
                  Route::get('/e-attendance-employees/excel',  [AttendanceReportController::class, 'getEAttendanceEmployeesExcelReport'])->name('attendance.reports.e-attendance-employees.report.excel');
            });
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END REPORTS ROUTES راپور های حاضری
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */


            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START HOLIDAYS ROUTES رخصتی های عمومی و اظطراری
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            Route::prefix('holidays')->group(function () {
                  // 1. DATA RETRIEVAL ROUTES
                  Route::get('/',  [HolidayController::class, 'getHolidays'])->name('attendance.holidays');
                  Route::get('/search',  [HolidayController::class, 'searchHolidays'])->name('attendance.holidays.search');
                  Route::get('/report/excel', [HolidayController::class, 'searchHolidaysForExcel'])->name('attendance.holidays.report.excel');

                  // 2. EDIT & UPDATE ROUTES
                  Route::get('/holiday/{id}/edit', [HolidayController::class, 'showEditForm'])->name('attendance.holiday.edit');
                  Route::post('/holiday/update', [HolidayController::class, 'update'])->name('attendance.holiday.update');

                  // 3. CREATE & STORE ROUTES
                  Route::get('/holiday/create', [HolidayController::class, 'showCreateForm'])->name('attendance.holiday.create');
                  Route::post('/holiday/store', [HolidayController::class, 'store'])->name('attendance.holiday.store');

                  // 4. DELETE ROUTES
                  Route::post('/holiday/delete', [HolidayController::class, 'delete'])->name('attendance.holiday.delete');
            });
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END HOLIDAYS ROUTES رخصتی های عمومی و اظطراری
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */



            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START ANNOUNCEMENTS ROUTES اطلاعیه ها
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            Route::prefix('announcements')->group(function () {
                  // 1. DATA RETRIEVAL ROUTES
                  Route::get('/',  [AnnouncementController::class, 'getAnnouncements'])->name('attendance.announcements');
                  Route::get('/search',  [AnnouncementController::class, 'searchAnnouncements'])->name('attendance.announcements.search');
                  Route::get('/report/excel', [AnnouncementController::class, 'searchAnnouncementsForExcel'])->name('attendance.announcements.report.excel');
                  Route::get('/rendered',  [AnnouncementController::class, 'getAnnouncementsRendered'])->name('attendance.announcements.rendered');

                  // 2. EDIT & UPDATE ROUTES
                  Route::get('/announcement/{id}/edit', [AnnouncementController::class, 'showEditForm'])->name('attendance.announcement.edit');
                  Route::post('/announcement/update', [AnnouncementController::class, 'update'])->name('attendance.announcement.update');

                  // 3. CREATE & STORE ROUTES
                  Route::get('/announcement/create', [AnnouncementController::class, 'showCreateForm'])->name('attendance.announcement.create');
                  Route::post('/announcement/store', [AnnouncementController::class, 'store'])->name('attendance.announcement.store');

                  // 4. DELETE ROUTES
                  Route::post('/announcement/delete', [AnnouncementController::class, 'delete'])->name('attendance.announcement.delete');
                  Route::get('/announcement/renderAnnouncementsList', [AnnouncementController::class, 'renderAnnouncementsList'])->name('attendance.announcement.renderAnnouncementsList');
            });
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END ANNOUNCEMENTS ROUTES اطلاعیه ها
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */



            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START CONTINUES ABSENT EMPLOYEES ROUTES اطلاعیه ها
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            Route::prefix('continues-absent')->group(function () {
                  // 1. DATA RETRIEVAL ROUTES
                  Route::get('/',  [AttendanceController::class, 'getContinuesAbsentEmployees'])->name('attendance.employees.continues-absent');
                  Route::get('/search',  [AttendanceController::class, 'searchContinuesAbsentEmployees'])->name('attendance.employees.continues-absent.search');
                  Route::get('/report/excel', [AttendanceController::class, 'searchContinuesAbsentEmployeesForExcel'])->name('attendance.employees.continues-absent.report.excel');

                  // 2. EDIT & UPDATE ROUTES
                  Route::post('/check', [AttendanceController::class, 'checkContinuesAbsentEmployees'])->name('attendance.employees.continues-absent.check');
                  Route::post('/mark/change', [AttendanceController::class, 'updateContinuesAbsentEmployeeMark'])->name('attendance.employee.continues-absent.change-mark');
                  Route::get('/employee/continues-absent/{id}',  [AttendanceController::class, 'getContinuesAbsentEmployeeDetails'])->name('attendance.employee.continues-absent.get');

                  // 3. CREATE & STORE ROUTES

                  // 4. DELETE ROUTES

            });
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | END CONTINUES ABSENT EMPLOYEES ROUTES اطلاعیه ها
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */



            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START MACHINES ROUTES ماشین های حاضری
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            Route::prefix('machines')->group(function () {
                  Route::get('machines', [MachinesController::class, 'index'])->name('attendance.machines');
                  Route::get('machines-settings', [MachinesController::class, 'settings'])->name('machines.settings');
                  Route::get('machines-settings-search', [MachinesController::class, 'settingsSearch'])->name('machines.settings.search');
                  Route::get('machines-stations', [StationController::class, 'stations'])->name('machines.stations');
                  Route::get('machines-stations-add', [StationController::class, 'addStation'])->name('machines.stations.add');
                  Route::get('machines-stations-edit', [StationController::class, 'editStation'])->name('machines.stations.edit');
                  Route::post('machines-stations-store', [StationController::class, 'storeStation'])->name('machines.stations.store');
                  Route::get('machines-stations-loadRendered', [StationController::class, 'loadStationRendered'])->name('machines.stations.loadRendered');
                  Route::put('attendance-update-station/{station}', [StationController::class, 'update'])->name('attendance.update.station');
                  Route::get('attendance-machineStatus', [MachinesController::class, 'machineStatus'])->name('attendance.machineStatus');
                  Route::get('attendance-add-machine', [MachinesController::class, 'addMachine'])->name('attendance.add.machine');
                  Route::get('attendance-edit-machine/{machine?}', [MachinesController::class, 'ediMachine'])->name('attendance.edit.machine');
                  Route::post('attendance-machine-update/{id}', [MachinesController::class, 'update'])->name('attendance.update.machine');
                  Route::post('attendance-store-machine', [MachinesController::class, 'store'])->name('attendance.store.machine');
                  Route::post('attendance-switch-machine', [MachinesController::class, 'switch'])->name('attendance.switch.machine');
                  Route::post('attendance-regImgTocken', [MachinesController::class, 'registerImgServerToken'])->name('attendance.imgServerToKen');
            });
            Route::prefix('fiximages')->group(function () {
                  Route::get('machines', [FixImagesController::class, 'index'])->name('fiximages.index');
            });
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START LEAVE REQUEST ROUTES درخواست رخصتی  
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            Route::prefix('leave-request')->group(function () {
                  Route::get('leave-request', [LeaveRequestController::class, 'index'])->name('leave_request.index');
                  Route::get('executive-leave-request/{id}', [LeaveRequestController::class, 'executiveIndex'])->name('executive_leave_request.index');
                  Route::get('leave-request-type-data', [LeaveRequestController::class, 'getLeaveRequestLeaveTypes'])->name('leave_request.type.data');
                  Route::get('leave-request-type-changed', [LeaveRequestController::class, 'leaveRequestLeaveTypesChanges'])->name('leave_request.type.changed');
                  Route::post('post-leave-request', [LeaveRequestController::class, 'postLeaveRequst'])->name('post.leave_request');
                  Route::get('manage-leave-request-index', [LeaveRequestController::class, 'manageLeaveRequst'])->name('manage.leave_request.index');
                  Route::get('manage-leave-request/search', [LeaveRequestController::class, 'searchLeaveRequests'])->name('attendance.leave-request.manage.search');
                  Route::get('leave-request-approve-template', [LeaveRequestController::class, 'getLeaveRequestApproveTemplate'])->name('leave_request.approve.template');
                  Route::post('post-leave-request-response', [LeaveRequestController::class, 'postLeaveRequestResponse'])->name('post.leave_request.approve.response');
                  Route::get('/leave-request/{id}/edit', [LeaveRequestController::class, 'showLeaveRequestEditForm'])->name('leave_request.edit');
                  Route::post('/leave-request/update', [LeaveRequestController::class, 'update'])->name('leave_request.update');
                  Route::get('/leaveRequests', [LeaveRequestController::class, 'getLeaveRequests'])->name('leave_requests.get');
                  Route::post('/leaveRequest/delete', [LeaveRequestController::class, 'delete'])->name('leave_request.delete');
                  Route::get('leave-request-show-details-template', [LeaveRequestController::class, 'getLeaveRequestShowDetailsTemplate'])->name('leave_request.show.details.template');
            });
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START LEAVE REQUEST NOTIFICATION ROUTES درخواست رخصتی نوتیفکیشن 
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            Route::prefix('leave-request-notification')->group(function () {
                  Route::get('leave-request-notification-fitch', [AttendanceNotificationController::class, 'fitch'])->name('leave_request.notification.fitch');
                  Route::get('leave-request-notification-counted', [AttendanceNotificationController::class, 'fitchCounted'])->name('leave_request.notification.counted');
            });
            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START STUDENTS ROUTES بخش محصلین            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            Route::prefix('students')->group(function () {
                  // 1. DATA RETRIEVAL ROUTES
                  Route::get('/',  [StudentsController::class, 'showStuents'])->name('students.show');
                  Route::get('search/students',  [StudentsController::class, 'searchStudents'])->name('attendance.students.search');
                  Route::get('/students/employees/search', [StudentsController::class, 'searchForStudentReg'])->name('student.emp.search');
                  Route::get('/students/employees/getSearched', [StudentsController::class, 'getSearchedForStudentReg'])->name('student.emp.getSearched');
                  Route::post('/students/employees/store', [StudentsController::class, 'store'])->name('students.employee.store');
                  Route::get('/students/employees/edit/{id}', [StudentsController::class, 'showEditForm'])->name('students.employee.edit');
                  Route::post('/students/employees/update', [StudentsController::class, 'update'])->name('students.employee.update');
                  Route::get('/students/employees/edit/shift/{id}', [StudentsController::class, 'showEditShiftForm'])->name('students.employee.edit.shift');
                  Route::post('/students/employees/edit/shift/update', [StudentsController::class, 'updateShift'])->name('students.employee.edit.shift.update');
                  Route::get('/report/excel', [StudentsController::class, 'searchStudentssForExcel'])->name('students.employee.report.excel');
            });


            Route::get('/{year}/{month}', [AttendanceController::class, 'showMyAttendace'])->name('attendance.my');
      });
      /*
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      | END ATTENDANCE MODULE ROUTES حاضری
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      */




      Route::prefix('forms')->group(function () {
            Route::get('/{type}', [FormController::class, 'downloadForm'])->name('forms.download');
            Route::get('/job-description/{type}', [FormController::class, 'downloadJobDescription'])->name('forms.download.job-description');
      });

      /*
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      | START AUTHENTICATION ROUTES کاربران، مادیول ها و مجوزهای
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      */
      Route::prefix('authentication')->group(function () {
            Route::post('auth-getUser', [UserController::class, 'getAIPUser'])->name('auth.getUser');
            Route::post('auth-getAuthAPIToken', [UserController::class, 'getAPIToken'])->name('auth.getAuthAPIToken');
            Route::post('auth-removeAuthAPIToken', [UserController::class, 'removeAPIToken'])->name('auth.removeAuthAPIToken');

            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START PERMISSIONS ROUTES مجوزهای
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            Route::prefix('permissions')->group(function () {
                  Route::get('list', [PermissionController::class, 'index'])->name('authentication.permissions');
                  Route::get('create', [PermissionController::class, 'create'])->name('authentication.permissions.create');
                  Route::post('store', [PermissionController::class, 'store'])->name('authentication.permissions.store');
                  Route::get('edit', [PermissionController::class, 'edit'])->name('authentication.permissions.edit');
                  Route::put('update/{permission}', [PermissionController::class, 'update'])->name('authentication.permissions.update');
                  Route::get('search', [PermissionController::class, 'search'])->name('authentication.permissions.search');
            });
            Route::prefix('preRole')->name('pre-role.')->group(function () {
                  Route::get('preRoles', [PreRoleController::class, 'preRoles'])->name('preRoles');
                  Route::get('addPreRoles', [PreRoleController::class, 'addPreRoles'])->name('addPreRoles');
                  Route::post('preRoleStore', [PreRoleController::class, 'preRoleStore'])->name('preRoleStore');
                  Route::get('preRoleEdit/{id}', [PreRoleController::class, 'preRoleEditForm'])->name('preRoleEdit');
                  Route::put('preRoleUpdate/{id}', [PreRoleController::class, 'preRoleUpdate'])->name('preRoleUpdate');
                  Route::get('preRoleSearch', [PreRoleController::class, 'preRoleSearch'])->name('preRoleSearch');
                  Route::get('renderPreRole', [PreRoleController::class, 'renderPreRole'])->name('renderPreRole');
                  Route::post('changePreRoleStatus', [PreRoleController::class, 'changePreRoleStatus'])->name('changePreRoleStatus');
            });


            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START ROLES ROUTES مادیول ها
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            Route::prefix('roles')->group(function () {
                  Route::get('list', [RoleController::class, 'index'])->name('authentication.roles');
                  Route::get('create', [RoleController::class, 'create'])->name('authentication.roles.create');
                  Route::post('store', [RoleController::class, 'store'])->name('roles.store');
                  Route::get('{role}/edit', [RoleController::class, 'edit'])->name('roles.edit');
                  Route::put('update/{role}', [RoleController::class, 'update'])->name('roles.update');
                  Route::get('search', [RoleController::class, 'search'])->name('roles.search');
                  Route::get('roles-permissions', [RoleController::class, 'getRolesPermissions'])->name('roles.permissions');
            });


            /*
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            | START USERS ROUTES کاربران
            |---------------------------------------------------------------------------------------------------------------------------------------------------------|
            */
            Route::prefix('users')->group(function () {
                  Route::get('list', [UserController::class, 'index'])->name('authentication.users');
                  Route::get('create', [UserController::class, 'create'])->name('authentication.users.create');
                  Route::get('search', [UserController::class, 'searchUser'])->name('users.search');
                  Route::get('users-deps', [UserController::class, 'getUserDeps'])->name('users.deps');
                  Route::get('employee-search-ajax', [UserController::class, 'searchEmpAsync'])->name('employee.search.ajax');
                  Route::get('load-employee-details', [UserController::class, 'loadEmployeeDetails'])->name('load.employee.details');
                  Route::get('user-all-dep', [UserController::class, 'getAllUserDeps'])->name('users.all.deps');
                  Route::get('user-get-all-deps', [UserController::class, 'getAllDepartments'])->name('users.getalldeps');
                  Route::get('user-stations', [UserController::class, 'getUserStations'])->name('users.stations');
                  Route::post('store/{action}', [UserController::class, 'store'])->name('users.store');
                  Route::get('edit/{id}', [UserController::class, 'edit'])->name('users.edit');
                  Route::get('user-view', [UserController::class, 'view'])->name('users.view');
                  Route::delete('remove', [UserController::class, 'destroy'])->name('users.remove');
                  Route::get('get-user-roles', [UserController::class, 'getUserRoles'])->name('get.user.roles');
                  Route::post('change-user-status', [UserController::class, 'changeUserStatus'])->name('change.user.status');
                  Route::get('view-user-status', [UserController::class, 'viewUserStatus'])->name('view.user.status');
                  Route::get('view-user-change-pass', [UserController::class, 'viewUserChangePass'])->name('view.user.change.pass');
                  Route::post('user-change-pass/{empID}', [UserController::class, 'userChangePass'])->name('user.change.pass');
            });
      });
      /*
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      | END AUTHENTICATION ROUTES کاربران و مجوزهای
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      */




      /*
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      | START MASTER DATA ROUTES
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      */
      Route::prefix('master-data')->group(function () {
            Route::get('home', [MasterDataHomeController::class, 'home'])->name('master-data.home');
            // 1. ETHNICITIES
            Route::prefix('ethnicities')->group(function () {
                  Route::get('/all', [EthnicityController::class, 'showAllEthnicitiesPage'])->name('master-data.ethnicity.all');
                  Route::get('/rendered', [EthnicityController::class, 'getEthnicities'])->name('master-data.ethnicity.rendered');
                  Route::get('/search', [EthnicityController::class, 'searchEthnicities'])->name('master-data.ethnicity.search');
                  Route::get('/create', [EthnicityController::class, 'showCreateForm'])->name('master-data.ethnicity.create');
                  Route::post('/store', [EthnicityController::class, 'store'])->name('master-data.ethnicity.store');
                  Route::get('/{id}/edit', [EthnicityController::class, 'showEditForm'])->name('master-data.ethnicity.edit');
                  Route::post('/update/{id}', [EthnicityController::class, 'update'])->name('master-data.ethnicity.update');
            });

            // 2. TRANSFER REASONS
            Route::prefix('transfer-reasons')->group(function () {
                  Route::get('/all', [TransferReasonController::class, 'showAllTransferReasonsPage'])->name('master-data.transfer-reason.all');
                  Route::get('/search', [TransferReasonController::class, 'searchTransferReasons'])->name('master-data.transfer-reason.search');
                  Route::get('/create', [TransferReasonController::class, 'showCreateForm'])->name('master-data.transfer-reason.create');
                  Route::post('/store', [TransferReasonController::class, 'store'])->name('master-data.transfer-reason.store');
                  Route::get('/{id}/edit', [TransferReasonController::class, 'showEditForm'])->name('master-data.transfer-reason.edit');
                  Route::post('/update/{id}', [TransferReasonController::class, 'update'])->name('master-data.transfer-reason.update');
            });

            // 3. COUNTRIES
            Route::prefix('countries')->group(function () {
                  Route::get('/all', [CountryController::class, 'showAllCountriesPage'])->name('master-data.country.all');
                  Route::get('/search', [CountryController::class, 'searchCountries'])->name('master-data.country.search');
                  Route::get('/create', [CountryController::class, 'showCreateForm'])->name('master-data.country.create');
                  Route::post('/store', [CountryController::class, 'store'])->name('master-data.country.store');
                  Route::get('/{id}/edit', [CountryController::class, 'showEditForm'])->name('master-data.country.edit');
                  Route::post('/update/{id}', [CountryController::class, 'update'])->name('master-data.country.update');
            });

            // 4. PROVINCES
            Route::prefix('provinces')->group(function () {
                  Route::get('/all', [ProvinceController::class, 'showAllProvincesPage'])->name('master-data.province.all');
                  Route::get('/rendered', [ProvinceController::class, 'getProvinces'])->name('master-data.province.rendered');
                  Route::get('/search', [ProvinceController::class, 'searchProvinces'])->name('master-data.province.search');
                  Route::get('/create', [ProvinceController::class, 'showCreateForm'])->name('master-data.province.create');
                  Route::post('/store', [ProvinceController::class, 'store'])->name('master-data.province.store');
                  Route::get('/{id}/edit', [ProvinceController::class, 'showEditForm'])->name('master-data.province.edit');
                  Route::post('/update/{id}', [ProvinceController::class, 'update'])->name('master-data.province.update');
            });

            // 5. DISTRICTS
            Route::prefix('districts')->group(function () {
                  Route::get('/all', [DistrictController::class, 'showAllDistrictsPage'])->name('master-data.district.all');
                  Route::get('/search', [DistrictController::class, 'searchDistricts'])->name('master-data.district.search');
                  Route::get('/province/{id}/districts', [DistrictController::class, 'getDistrictsByProvinceIdRendered'])->name('master-data.district.province.rendered');
                  Route::get('/create', [DistrictController::class, 'showCreateForm'])->name('master-data.district.create');
                  Route::post('/store', [DistrictController::class, 'store'])->name('master-data.district.store');
                  Route::get('/{id}/edit', [DistrictController::class, 'showEditForm'])->name('master-data.district.edit');
                  Route::post('/update/{id}', [DistrictController::class, 'update'])->name('master-data.district.update');
            });

            // 6. VILLAGES
            Route::prefix('villages')->group(function () {
                  Route::get('/all', [VillageController::class, 'showAllVillagesPage'])->name('master-data.village.all');
                  Route::get('/search', [VillageController::class, 'searchVillages'])->name('master-data.village.search');
                  Route::get('/district/{id}/villages', [VillageController::class, 'getVillagesByDistrictIdRendered'])->name('master-data.village.district.rendered');
                  Route::get('/create', [VillageController::class, 'showCreateForm'])->name('master-data.village.create');
                  Route::post('/store', [VillageController::class, 'store'])->name('master-data.village.store');
                  Route::get('/{id}/edit', [VillageController::class, 'showEditForm'])->name('master-data.village.edit');
                  Route::post('/update/{id}', [VillageController::class, 'update'])->name('master-data.village.update');
            });

            // 7. UNIVERSITIES
            Route::prefix('universities')->group(function () {
                  Route::get('/all', [UniversityController::class, 'showAllUniversitiesPage'])->name('master-data.university.all');
                  Route::get('/search', [UniversityController::class, 'searchUniversities'])->name('master-data.university.search');
                  Route::get('/create', [UniversityController::class, 'showCreateForm'])->name('master-data.university.create');
                  Route::post('/store', [UniversityController::class, 'store'])->name('master-data.university.store');
                  Route::get('/{id}/edit', [UniversityController::class, 'showEditForm'])->name('master-data.university.edit');
                  Route::post('/update/{id}', [UniversityController::class, 'update'])->name('master-data.university.update');
            });

            // 8. ACADEMIC FIELD CATEGORIES
            Route::prefix('academic-field-categories')->group(function () {
                  Route::get('/all', [AcademicFieldCategoryController::class, 'showAllAcademicFieldCategoriesPage'])->name('master-data.academic-field-category.all');
                  Route::get('/search', [AcademicFieldCategoryController::class, 'searchAcademicFieldCategories'])->name('master-data.academic-field-category.search');
                  Route::get('/create', [AcademicFieldCategoryController::class, 'showCreateForm'])->name('master-data.academic-field-category.create');
                  Route::post('/store', [AcademicFieldCategoryController::class, 'store'])->name('master-data.academic-field-category.store');
                  Route::get('/{id}/edit', [AcademicFieldCategoryController::class, 'showEditForm'])->name('master-data.academic-field-category.edit');
                  Route::post('/update/{id}', [AcademicFieldCategoryController::class, 'update'])->name('master-data.academic-field-category.update');
            });

            // 9. ACADEMIC FIELDS
            Route::prefix('academic-fields')->group(function () {
                  Route::get('/all', [AcademicFieldController::class, 'showAllAcademicFieldsPage'])->name('master-data.academic-field.all');
                  Route::get('/search', [AcademicFieldController::class, 'searchAcademicFields'])->name('master-data.academic-field.search');
                  Route::get('/create', [AcademicFieldController::class, 'showCreateForm'])->name('master-data.academic-field.create');
                  Route::post('/store', [AcademicFieldController::class, 'store'])->name('master-data.academic-field.store');
                  Route::get('/{id}/edit', [AcademicFieldController::class, 'showEditForm'])->name('master-data.academic-field.edit');
                  Route::post('/update/{id}', [AcademicFieldController::class, 'update'])->name('master-data.academic-field.update');
            });

            // 10. GOVERNMENT OFFICES
            Route::prefix('government-offices')->group(function () {
                  Route::get('/all', [GovernmentOfficeController::class, 'showAllGovernmentOfficesPage'])->name('master-data.government-office.all');
                  Route::get('/search', [GovernmentOfficeController::class, 'searchGovernmentOffices'])->name('master-data.government-office.search');
                  Route::get('/create', [GovernmentOfficeController::class, 'showCreateForm'])->name('master-data.government-office.create');
                  Route::post('/store', [GovernmentOfficeController::class, 'store'])->name('master-data.government-office.store');
                  Route::get('/{id}/edit', [GovernmentOfficeController::class, 'showEditForm'])->name('master-data.government-office.edit');
                  Route::post('/update/{id}', [GovernmentOfficeController::class, 'update'])->name('master-data.government-office.update');
            });

            // 11. EDUCATION DEGREES
            Route::prefix('education-degrees')->group(function () {
                  Route::get('/all', [EducationDegreeController::class, 'showAllEducationDegreesPage'])->name('master-data.education-degree.all');
                  Route::get('/search', [EducationDegreeController::class, 'searchEducationDegrees'])->name('master-data.education-degree.search');
                  Route::get('/create', [EducationDegreeController::class, 'showCreateForm'])->name('master-data.education-degree.create');
                  Route::post('/store', [EducationDegreeController::class, 'store'])->name('master-data.education-degree.store');
                  Route::get('/{id}/edit', [EducationDegreeController::class, 'showEditForm'])->name('master-data.education-degree.edit');
                  Route::post('/update/{id}', [EducationDegreeController::class, 'update'])->name('master-data.education-degree.update');
            });

            // 12. MOQARARI APPROVAL AUTHORITIES
            Route::prefix('moqarari-approval-authorities')->group(function () {
                  Route::get('/all', [MoqarariApprovalAuthorityController::class, 'getMoqarariApprovalAuthorities'])->name('master-data.moqarari-approval-authority.all');
                  Route::get('/rendered', [MoqarariApprovalAuthorityController::class, 'getMoqarariApprovalAuthoritiesRendered'])->name('master-data.moqarari-approval-authority.rendered');
                  Route::get('/search', [MoqarariApprovalAuthorityController::class, 'searchMoqarariApprovalAuthorities'])->name('master-data.moqarari-approval-authority.search');
                  Route::get('/create', [MoqarariApprovalAuthorityController::class, 'showCreateForm'])->name('master-data.moqarari-approval-authority.create');
                  Route::post('/store', [MoqarariApprovalAuthorityController::class, 'store'])->name('master-data.moqarari-approval-authority.store');
                  Route::get('/{id}/edit', [MoqarariApprovalAuthorityController::class, 'showEditForm'])->name('master-data.moqarari-approval-authority.edit');
                  Route::post('/update/{id}', [MoqarariApprovalAuthorityController::class, 'update'])->name('master-data.moqarari-approval-authority.update');
            });

            // 13. CONTRACT SUSPENSION REASONS
            Route::prefix('contract-suspension-reasons')->group(function () {
                  Route::get('/all', [ContractSuspensionReasonController::class, 'showAllContractSuspensionReasonsPage'])->name('master-data.contract.suspension-reason.all');
                  Route::get('/rendered/all', [ContractSuspensionReasonController::class, 'getAllContractSuspensionReasonsRendered'])->name('master-data.contract.suspension-reason.render.all');
                  Route::get('/search', [ContractSuspensionReasonController::class, 'searchContractSuspensionReasons'])->name('master-data.contract.suspension-reason.search');
                  Route::get('/create', [ContractSuspensionReasonController::class, 'showCreateForm'])->name('master-data.contract.suspension-reason.create');
                  Route::post('/store', [ContractSuspensionReasonController::class, 'store'])->name('master-data.contract.suspension-reason.store');
                  Route::get('/{id}/edit', [ContractSuspensionReasonController::class, 'showEditForm'])->name('master-data.contract.suspension-reason.edit');
                  Route::post('/update/{id}', [ContractSuspensionReasonController::class, 'update'])->name('master-data.contract.suspension-reason.update');
            });

            // 14. CONTRACT CANCELLATION REASONS
            Route::prefix('contract-cancellation-reasons')->group(function () {
                  Route::get('/all', [ContractCancellationReasonController::class, 'showAllContractCancellationReasonsPage'])->name('master-data.contract.cancellation-reason.all');
                  Route::get('/rendered/all', [ContractCancellationReasonController::class, 'getAllContractCancellationReasonsRendered'])->name('master-data.contract.cancellation-reason.render.all');
                  Route::get('/search', [ContractCancellationReasonController::class, 'searchContractCancellationReasons'])->name('master-data.contract.cancellation-reason.search');
                  Route::get('/create', [ContractCancellationReasonController::class, 'showCreateForm'])->name('master-data.contract.cancellation-reason.create');
                  Route::post('/store', [ContractCancellationReasonController::class, 'store'])->name('master-data.contract.cancellation-reason.store');
                  Route::get('/{id}/edit', [ContractCancellationReasonController::class, 'showEditForm'])->name('master-data.contract.cancellation-reason.edit');
                  Route::post('/update/{id}', [ContractCancellationReasonController::class, 'update'])->name('master-data.contract.cancellation-reason.update');
            });

            // 15. EMPLOYEE FIRE REASONS
            Route::prefix('fire-reasons')->group(function () {
                  Route::get('/all', [FireReasonController::class, 'getFireReasons'])->name('master-data.fire-reason.all');
                  Route::get('/search', [FireReasonController::class, 'searchFireReasons'])->name('master-data.fire-reason.search');
                  Route::get('/create', [FireReasonController::class, 'showCreateForm'])->name('master-data.fire-reason.create');
                  Route::post('/store', [FireReasonController::class, 'store'])->name('master-data.fire-reason.store');
                  Route::get('/{id}/edit', [FireReasonController::class, 'showEditForm'])->name('master-data.fire-reason.edit');
                  Route::post('/update/{id}', [FireReasonController::class, 'update'])->name('master-data.fire-reason.update');
            });

            // 16. EMPLOYEE TANQIS REASONS
            Route::prefix('tanqis-reasons')->group(function () {
                  Route::get('/all', [TanqisReasonController::class, 'getTanqisReasons'])->name('master-data.tanqis-reason.all');
                  Route::get('/search', [TanqisReasonController::class, 'searchTanqisReasons'])->name('master-data.tanqis-reason.search');
                  Route::get('/create', [TanqisReasonController::class, 'showCreateForm'])->name('master-data.tanqis-reason.create');
                  Route::post('/store', [TanqisReasonController::class, 'store'])->name('master-data.tanqis-reason.store');
                  Route::get('/{id}/edit', [TanqisReasonController::class, 'showEditForm'])->name('master-data.tanqis-reason.edit');
                  Route::post('/update/{id}', [TanqisReasonController::class, 'update'])->name('master-data.tanqis-reason.update');
            });

            // 17. EMPLOYEE EVALUATION RESULTS
            Route::prefix('/employee/evaluation-results')->group(function () {
                  Route::get('/all', [EmployeeEvaluationResultController::class, 'getEmployeeEvaluationResults'])->name('master-data.employee.evaluation-result.all');
                  Route::get('/search', [EmployeeEvaluationResultController::class, 'searchEmployeeEvaluationResults'])->name('master-data.employee.evaluation-result.search');
                  Route::get('/create', [EmployeeEvaluationResultController::class, 'showCreateForm'])->name('master-data.employee.evaluation-result.create');
                  Route::post('/store', [EmployeeEvaluationResultController::class, 'store'])->name('master-data.employee.evaluation-result.store');
                  Route::get('/{id}/edit', [EmployeeEvaluationResultController::class, 'showEditForm'])->name('master-data.employee.evaluation-result.edit');
                  Route::post('/update/{id}', [EmployeeEvaluationResultController::class, 'update'])->name('master-data.employee.evaluation-result.update');
            });

            // EMPLOYEE EVALUATION RECOMMENDATIONS
            Route::prefix('/employee/evaluation-recommendations')->group(function () {
                  Route::get('/all', [EmployeeEvaluationRecommendationController::class, 'getEmployeeEvaluationRecommendations'])->name('master-data.employee.evaluation-recommendation.all');
                  Route::get('/create', [EmployeeEvaluationRecommendationController::class, 'showCreateForm'])->name('master-data.employee.evaluation-recommendation.create');
                  Route::post('/store', [EmployeeEvaluationRecommendationController::class, 'store'])->name('master-data.employee.evaluation-recommendation.store');
                  Route::get('/{id}/edit', [EmployeeEvaluationRecommendationController::class, 'showEditForm'])->name('master-data.employee.evaluation-recommendation.edit');
                  Route::post('/update/{id}', [EmployeeEvaluationRecommendationController::class, 'update'])->name('master-data.employee.evaluation-recommendation.update');
                  Route::get('/search', [EmployeeEvaluationRecommendationController::class, 'searchEmployeeEvaluationRecommendation'])->name('master-data.employee.evaluation-recommendation.search');
            });

            // 18. EMPLOYEE QADAMS
            Route::prefix('/employee/qadams')->group(function () {
                  Route::get('/all', [EmployeeQadamController::class, 'getEmployeeQadams'])->name('master-data.employee.qadam.all');
                  Route::get('/search', [EmployeeQadamController::class, 'searchEmployeeQadams'])->name('master-data.employee.qadam.search');
                  Route::get('/create', [EmployeeQadamController::class, 'showCreateForm'])->name('master-data.employee.qadam.create');
                  Route::post('/store', [EmployeeQadamController::class, 'store'])->name('master-data.employee.qadam.store');
                  Route::get('/{id}/edit', [EmployeeQadamController::class, 'showEditForm'])->name('master-data.employee.qadam.edit');
                  Route::post('/update/{id}', [EmployeeQadamController::class, 'update'])->name('master-data.employee.qadam.update');
            });

            // 19. EMPLOYEE MAKAFAT
            Route::prefix('/employee/makafat/types')->group(function () {
                  Route::get('/all', [MakafatTypeController::class, 'getMakafatTypes'])->name('master-data.employee.makafat.type.all');
                  Route::get('/search', [MakafatTypeController::class, 'searchMakafatTypes'])->name('master-data.employee.makafat.type.search');
                  Route::get('/create', [MakafatTypeController::class, 'showCreateForm'])->name('master-data.employee.makafat.type.create');
                  Route::post('/store', [MakafatTypeController::class, 'store'])->name('master-data.employee.makafat.type.store');
                  Route::get('/{id}/edit', [MakafatTypeController::class, 'showEditForm'])->name('master-data.employee.makafat.type.edit');
                  Route::post('/update/{id}', [MakafatTypeController::class, 'update'])->name('master-data.employee.makafat.type.update');
            });

            // 20. PUNISHMENT TYPES
            Route::prefix('/employee/punishment/types')->group(function () {
                  Route::get('/all', [PunishmentTypeController::class, 'getPunishmentTypes'])->name('master-data.employee.punishment.type.all');
                  Route::get('/search', [PunishmentTypeController::class, 'searchPunishmentTypes'])->name('master-data.employee.punishment.type.search');
                  Route::get('/create', [PunishmentTypeController::class, 'showCreateForm'])->name('master-data.employee.punishment.type.create');
                  Route::post('/store', [PunishmentTypeController::class, 'store'])->name('master-data.employee.punishment.type.store');
                  Route::get('/{id}/edit', [PunishmentTypeController::class, 'showEditForm'])->name('master-data.employee.punishment.type.edit');
                  Route::post('/update/{id}', [PunishmentTypeController::class, 'update'])->name('master-data.employee.punishment.type.update');
            });

            // 21. PUNISHMENT REASONS
            Route::prefix('/employee/punishment/reasons')->group(function () {
                  Route::get('/all', [PunishmentReasonController::class, 'getPunishmentReasons'])->name('master-data.employee.punishment.reason.all');
                  Route::get('/search', [PunishmentReasonController::class, 'searchPunishmentReasons'])->name('master-data.employee.punishment.reason.search');
                  Route::get('/create', [PunishmentReasonController::class, 'showCreateForm'])->name('master-data.employee.punishment.reason.create');
                  Route::post('/store', [PunishmentReasonController::class, 'store'])->name('master-data.employee.punishment.reason.store');
                  Route::get('/{id}/edit', [PunishmentReasonController::class, 'showEditForm'])->name('master-data.employee.punishment.reason.edit');
                  Route::post('/update/{id}', [PunishmentReasonController::class, 'update'])->name('master-data.employee.punishment.reason.update');
            });

            // 22. TRANING TYPES
            Route::prefix('/employee/training/types')->group(function () {
                  Route::get('/all', [TrainingTypeController::class, 'getTrainingTypes'])->name('master-data.employee.training.type.all');
                  Route::get('/search', [TrainingTypeController::class, 'searchTrainingTypes'])->name('master-data.employee.training.type.search');
                  Route::get('/create', [TrainingTypeController::class, 'showCreateForm'])->name('master-data.employee.training.type.create');
                  Route::post('/store', [TrainingTypeController::class, 'store'])->name('master-data.employee.training.type.store');
                  Route::get('/{id}/edit', [TrainingTypeController::class, 'showEditForm'])->name('master-data.employee.training.type.edit');
                  Route::post('/update/{id}', [TrainingTypeController::class, 'update'])->name('master-data.employee.training.type.update');
            });

            // 23. ASKARI TYPES
            Route::prefix('/employee/askari/types')->group(function () {
                  Route::get('/all', [AskariTypeController::class, 'getAskariTypes'])->name('master-data.employee.askari.type.all');
                  Route::get('/search', [AskariTypeController::class, 'searchAskariTypes'])->name('master-data.employee.askari.type.search');
                  Route::get('/create', [AskariTypeController::class, 'showCreateForm'])->name('master-data.employee.askari.type.create');
                  Route::post('/store', [AskariTypeController::class, 'store'])->name('master-data.employee.askari.type.store');
                  Route::get('/{id}/edit', [AskariTypeController::class, 'showEditForm'])->name('master-data.employee.askari.type.edit');
                  Route::post('/update/{id}', [AskariTypeController::class, 'update'])->name('master-data.employee.askari.type.update');
            });

            // 24. GUARANTEE TYPES
            Route::prefix('/employee/guarantee/types')->group(function () {
                  Route::get('/all', [GuaranteeTypeController::class, 'getGuaranteeTypes'])->name('master-data.employee.guarantee.type.all');
                  Route::get('/search', [GuaranteeTypeController::class, 'searchGuaranteeTypes'])->name('master-data.employee.guarantee.type.search');
                  Route::get('/create', [GuaranteeTypeController::class, 'showCreateForm'])->name('master-data.employee.guarantee.type.create');
                  Route::post('/store', [GuaranteeTypeController::class, 'store'])->name('master-data.employee.guarantee.type.store');
                  Route::get('/{id}/edit', [GuaranteeTypeController::class, 'showEditForm'])->name('master-data.employee.guarantee.type.edit');
                  Route::post('/update/{id}', [GuaranteeTypeController::class, 'update'])->name('master-data.employee.guarantee.type.update');
            });

            // 25. GUARANTEE RANGES
            Route::prefix('/employee/guarantee/ranges')->group(function () {
                  Route::get('/all', [GuaranteeRangeController::class, 'getGuaranteeRanges'])->name('master-data.employee.guarantee.range.all');
                  Route::get('/search', [GuaranteeRangeController::class, 'searchGuaranteeRanges'])->name('master-data.employee.guarantee.range.search');
                  Route::get('/create', [GuaranteeRangeController::class, 'showCreateForm'])->name('master-data.employee.guarantee.range.create');
                  Route::post('/store', [GuaranteeRangeController::class, 'store'])->name('master-data.employee.guarantee.range.store');
                  Route::get('/{id}/edit', [GuaranteeRangeController::class, 'showEditForm'])->name('master-data.employee.guarantee.range.edit');
                  Route::post('/update/{id}', [GuaranteeRangeController::class, 'update'])->name('master-data.employee.guarantee.range.update');
            });

            // 26. LEAVE TYPES
            Route::prefix('/attendance/leave/types')->group(function () {
                  Route::get('/all', [LeaveTypeController::class, 'getLeaveTypes'])->name('master-data.attendance.leave.type.all');
                  Route::get('/rendered', [LeaveTypeController::class, 'getLeaveTypesRendered'])->name('master-data.attendance.leave.type.rendered');
                  Route::get('/search', [LeaveTypeController::class, 'searchLeaveTypes'])->name('master-data.attendance.leave.type.search');
                  Route::get('/create', [LeaveTypeController::class, 'showCreateForm'])->name('master-data.attendance.leave.type.create');
                  Route::post('/store', [LeaveTypeController::class, 'store'])->name('master-data.attendance.leave.type.store');
                  Route::get('/{id}/edit', [LeaveTypeController::class, 'showEditForm'])->name('master-data.attendance.leave.type.edit');
                  Route::post('/update/{id}', [LeaveTypeController::class, 'update'])->name('master-data.attendance.leave.type.update');
            });

            // leave request valid date
            Route::prefix('/attendance/leave/request/valid/date')->group(function () {
                  Route::get('/all', [LeaveRequestValidDateController::class, 'getLeaveRequestValidDate'])->name('master-data.attendance.leave.request.valid.date.all');
                  Route::get('/search', [LeaveRequestValidDateController::class, 'searchLeaveRequestValidDate'])->name('master-data.attendance.leave.request.valid.date.search');
                  Route::get('/{id}/edit', [LeaveRequestValidDateController::class, 'edit'])->name('master-data.attendance.leave.request.valid.date.edit');
                  Route::post('/update/{id}', [LeaveRequestValidDateController::class, 'update'])->name('master-data.attendance.leave.request.valid.date.update');
            });

            // 27. CARD PERIOD
            Route::prefix('/cards/periods')->name('master-data.periods.')->group(function () {
                  Route::get('/all', [CardPeriodController::class, 'getCardPeriods'])->name('get');
                  Route::get('/search', [CardPeriodController::class, 'search'])->name('search');
                  Route::get('/create', [CardPeriodController::class, 'create'])->name('create');
                  Route::post('/store', [CardPeriodController::class, 'store'])->name('store');
                  Route::post('/update/{id}', [CardPeriodController::class, 'update'])->name('update');
                  Route::get('/edit/{id}/', [CardPeriodController::class, 'edit'])->name('edit');
                  Route::post('/status', [CardPeriodController::class, 'status'])->name('status');
            });
            // 28. CARD TYPES
            Route::prefix('/cards/types')->name('master-data.card-types.')->group(function () {
                  Route::get('/all', [CardTypeController::class, 'getCardTypes'])->name('get');
                  Route::get('/search', [CardTypeController::class, 'search'])->name('search');
                  Route::get('/create', [CardTypeController::class, 'create'])->name('create');
                  Route::post('/store', [CardTypeController::class, 'store'])->name('store');
                  Route::post('/update/{id}', [CardTypeController::class, 'update'])->name('update');
                  Route::get('/edit/{id}/', [CardTypeController::class, 'edit'])->name('edit');
                  Route::post('/status', [CardTypeController::class, 'status'])->name('status');
            });
            // 29. BLOOD GROUP
            Route::prefix('blood-groups')->group(function () {
                  Route::get('/rendered', [BloodGroupController::class, 'getBloodGroups'])->name('master-data.blood-group.rendered');
            });

            // 30. NTS grids
            Route::prefix('nta-grids')->group(function () {
                  Route::get('/all', [NtaGridController::class, 'getNTAGrids'])->name('master-data.nta-grid.all');
                  Route::get('/search', [NtaGridController::class, 'searchNtaGrids'])->name('master-data.nta-grid.search');
                  Route::get('/create', [NtaGridController::class, 'showCreateForm'])->name('master-data.nta-grid.create');
                  Route::post('/store', [NtaGridController::class, 'store'])->name('master-data-nta-grid.store');
                  Route::get('/{id}/edit', [NtaGridController::class, 'showEditForm'])->name('master-data.nta-grid.edit');
                  Route::post('/{id}/update', [NtaGridController::class, 'update'])->name('master-data.nta-grid.update');
            });

            // 31. NTA steps
            Route::prefix('nta-steps')->group(function () {
                  Route::get('/all', [NtaStepController::class, 'getNTASteps'])->name('master-data.nta-step.all');
                  Route::get('/search', [NtaStepController::class, 'searchNtaSteps'])->name('master-data.nta-step.search');
                  Route::get('/create', [NtaStepController::class, 'showCreateForm'])->name('master-data.nta-step.create');
                  Route::post('/store', [NtaStepController::class, 'store'])->name('master-data-nta-step.store');
                  Route::get('/{id}/edit', [NtaStepController::class, 'showEditForm'])->name('master-data.nta-step.edit');
                  Route::post('/{id}/update', [NtaStepController::class, 'update'])->name('master-data.nta-step.update');
            });

            // 32. NTA salaries
            Route::prefix('nta-salaries')->group(function () {
                  Route::get('/all', [NtaSalaryController::class, 'getNtaSalaries'])->name('master-data.nta-salary.all');
                  Route::get('/search', [NtaSalaryController::class, 'searchNtaSalaries'])->name('master-data.nta-salary.search');
                  Route::get('/create', [NtaSalaryController::class, 'showCreateForm'])->name('master-data.nta-salary.create');
                  Route::post('/store', [NtaSalaryController::class, 'store'])->name('master-data.nta-salary.store');
                  Route::get('/{id}/edit', [NtaSalaryController::class, 'showEditForm'])->name('master-data.nta-salary.edit');
                  Route::post('/{id}/update', [NtaSalaryController::class, 'update'])->name('master-data.nta-salary.update');
            });

            Route::prefix('job_description_template')->group(function(){
                  Route::get('/all', [JobDescriptionTemplateController::class, 'index'])->name('master-data.job-description-template.all');
                  Route::get('/search', [JobDescriptionTemplateController::class, 'searchJobDescriptionTemplates'])->name('master-data.job-description-template.search');
                  Route::get('/create', [JobDescriptionTemplateController::class, 'showCreateForm'])->name('master-data.job-description-template.create');
                  Route::post('/sotre', [JobDescriptionTemplateController::class, 'store'])->name('master-data.job-description-template.store');
                  Route::get('/{id}/edit', [JobDescriptionTemplateController::class, 'showEditForm'])->name('master-data.job-description-template.edit');
                  Route::post('/update/{id}', [JobDescriptionTemplateController::class, 'update'])->name('master-data.job-description-template.update');
            });

            Route::prefix('forms')->group(function(){
                  Route::get('/all', [FormsController::class, 'index'])->name('master-data.forms.all');
                  Route::get('/search', [FormsController::class, 'searchForms'])->name('master-data.form.search');
                  Route::get('/create', [FormsController::class, 'showCreateForm'])->name('master-data.form.create');
                  Route::post('/store', [FormsController::class, 'store'])->name('master-data.form.store');
                  Route::get('/{id}/edit', [FormsController::class, 'showEditForm'])->name('master-data.form.edit');
                  Route::put('/update/{id}', [FormsController::class, 'update'])->name('master-data.form.update');
            });

            /*
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      | END MASTER DATA ROUTES
      |---------------------------------------------------------------------------------------------------------------------------------------------------------|
      */
      });
});
/**
 * login routes
 */
Route::get('login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('login', [LoginController::class, 'login']);
Route::post('logout', [LoginController::class, 'logout'])->name('logout');
