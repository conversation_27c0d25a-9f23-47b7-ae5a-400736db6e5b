<x-layouts.master title="{{ !is_null($data['record']) ? __('general.update_guarantee_type') : __('general.new_form') }}">

    @php
        $record = $data['record'];
        $is_editing = !is_null($record);
    @endphp

    @push('content')
        <x-pages.pagesContainer>

            <div class="pb-2">
                <x-pages.master_data.links />
            </div>

            <form
                action="{{ $is_editing ? route('master-data.employee.guarantee.type.update', ['id' => encrypt($record->id)]) : route('master-data.form.store') }}"
                method="POST" enctype="multipart/form-data">
                @csrf

                <div class="card">
                    <div class="card-header d-flex justify-content-between alig-items-center border-bottom">
                        <h4 class="m-0">
                            {{ $is_editing ? __('general.update_guarantee_type') : __('general.new_form') }}
                        </h4>
                        <div class="d-flex align-items-center">
                            <button type="submit" style="padding-top: 5px; padding-bottom: 6px;"
                                class="btn btn-sm btn-primary mx-2">{{ $is_editing ? __('general.update') : __('general.create') }}
                            </button>
                            <x-tools.utilities.back_button to="master-data.forms.all" />
                        </div>
                    </div>

                    <div class="card-body p-0">
                        <div class="row">
                            <div class="col-12">
                                <div class="p-4">
                                    <div class="row">
                                        {{-- NAME PASHTO --}}
                                        <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                            <label for="name_ps" class="form-label">{{ __('general.name_ps') }}</label>
                                            <input name="name_ps" id="name_ps" class="form-control" type="text"
                                                value="{{ $is_editing ? $record->name_ps : old('name_ps') }}"
                                                placeholder="{{ __('general.name_ps') }}" required>
                                            @error('name_ps')
                                                <div class="error">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        {{-- NAME DARI --}}
                                        <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                            <label for="name_dr" class="form-label">{{ __('general.name_dr') }}</label>
                                            <input name="name_dr" id="name_dr" class="form-control" type="text"
                                                value="{{ $is_editing ? $record->name_dr : old('name_dr') }}"
                                                placeholder="{{ __('general.name_dr') }}" required>
                                            @error('name_dr')
                                                <div class="error">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        {{-- NAME ENGLISH --}}
                                        <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                            <label for="name_en" class="form-label">{{ __('general.name_en') }}</label>
                                            <input name="name_en" id="name_en" class="form-control" type="text"
                                                value="{{ $is_editing ? $record->name_en : old('name_en') }}"
                                                placeholder="{{ __('general.name_en') }}">
                                            @error('name_en')
                                                <div class="error">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="row">
                                            <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                                <label for="form" class="form-label">{{ __('general.form') }}</label>
                                                <input name="form" id="form" class="form-control" type="file"
                                                    accept=".pdf" />
                                                @error('form')
                                                    <div class="error">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        @if ($is_editing && !empty($record->path))
                                            <div class="mt-5">
                                                @php
                                                    $year = explode('_', $record->path)[0];
                                                @endphp

                                                <embed src="{{ URL::to('/') . '/storage' . '/' . $data['path'] }}"
                                                    style="width: 100%;  height: 80vh;" />
                                            </div>
                                        @endif
                                    </div>

                                    <br>

                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer border-top">
                        <button type="submit"
                            class="btn btn-primary">{{ $is_editing ? __('general.update') : __('general.create') }}
                        </button>
                    </div>
                </div>
            </form>
        </x-pages.pagesContainer>
    @endpush
</x-layouts.master>
