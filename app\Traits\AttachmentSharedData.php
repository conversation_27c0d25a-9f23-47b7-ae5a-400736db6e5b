<?php
namespace App\Traits;
trait AttachmentSharedData
{


// root of all attachments directories
    public static $_ROOT_DIRECTORY = 'documents/hr-attachments';

// static names
    public static $_CARDS_TEMP_FRONT_NAME = 'front-temp';
    public static $_CARDS_TEMP_BACK_NAME = 'back-temp';

// directories of rootDir
    public static $_ID_CARD_ATTACHMENT_DIRECTORY = 'id-card-attachments';
    public static $_PROFILE_PICTURE_ATTACHMENT_DIRECTORY = 'profile-pictures';
    public static $_CARD_WATERMARK_ATTACHMENT_DIRECTORY = 'temps/card-watermark';
    public static $_JOB_DESCRIPTION_ATTACHMENT_DIRECTORY = 'job-descriptions';
    public static $_FORM_ATTACHMENT_DIRECTORY = 'forms';
}
