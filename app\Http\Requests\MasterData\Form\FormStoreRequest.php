<?php

namespace App\Http\Requests\MasterData\Form;

use Illuminate\Foundation\Http\FormRequest;

class FormStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'name_ps' => 'required',
            'name_dr' => 'required',
            'form' => 'required|file|mimes:pdf,doc,docx',
        ];

        return $rules;
    }
}
