<?php

return [
    'hr_app_title' => 'Human Resource Management System',
    'search' => 'Search',
    'empID' => 'ID',
    'notfound' => 'Not Found!',
    'select_date' => 'Select Date',
    'store' => 'Store',
    'status' => 'Status',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'active' => 'Active',
    'no' => 'num',
    'actions' => 'Actions',
    'cancel' => 'Cancel',
    'add' => 'Add',
    'created_success' => 'Successfully created',
    'updated_success' => 'Successfully updated',
    'name' => 'Name',
    'color' => 'Color',
    'type' => 'type',
    'language' => 'Language',
    'print_status' => 'Print Status',
    'count' => 'Count',
    'year' => 'Year',
    'expire_date' => 'Expire Date',
    'save' => 'Save',
    'update' => 'Update',
    'user' => 'User',
    'last_name' => 'Last Name',
    'full_name' => 'Full name',
    'current_job' => 'Current Job',
    'email' => 'Email',
    'code' => 'Code',
    'name_en' => 'Name English',
    'name_dr' => 'Name Persion',
    'name_ps' => 'Name Pashto',
    'bast' => 'Bast',
    'branches' => 'Branches',
    'provinces' => 'Provinces',
    'skill' => 'Skill',
    'mulki' => 'Mulki',
    'submit' => 'submit',
    'main_skill' => 'Main Skill',
    'extra_skill' => 'Extra Skill',
    'no' => 'No',
    'delete_item' => 'Are you sure you want to delete this item?',
    'utilities' => 'Utilities',
    'Tashkilat' => 'Tashkeelat',
    'authentication' => 'Authentication',
    'app_settings' => 'App Settings',
    'footer_content' => 'Footer Content',
    'site_title' => 'Site Title',
    'choose_file' => 'Choose File',
    'password' => 'Password',
    'confirm_password' => 'Confirm Password',
    'select_branch' => 'Select Branch',
    'select_roles' => 'Select Module',
    'direct_permissions' => 'Direct Permissions',
    'role' => 'Role',
    'permissions' => 'Permissions',
    'permission' => 'Permission',
    'select_year' => 'Select Year',
    'code_number' => 'Code Number',
    'type_code_number' => 'Type Code Number',
    'departments' => 'Departments',
    'tashkeel' => 'Tashkeel',
    'moror_tashkeelat' => 'Moror Tashkeelat',
    'province' => 'province',
    'general_department' => 'General Department',
    'select' => 'Select',
    'close' => 'Close',
    'newbast' => 'New Bast',
    'upgrade' => 'Upgrade',
    'downgrade' => 'Downgrade',
    'transfare' => 'Transfare',
    'parent_department' => 'Parent Department',
    'child_departments' => 'Child Departments',
    'title_en' => 'Title English',
    'title_dr' => 'Tilte Persion',
    'title_ps' => 'Title Pashto',
    'department_tree_view' => 'Department Tree View',
    'roles' => 'Module',
    'department' => "Department",
    'date' => 'Date',
    'rob' => 'Rob',
    'firstRob' => 'First Rob',
    'secondRob' => 'Second Rob',
    'thirdRob' => 'Third Rob',
    'fourthRob' => 'Fourth Rob',
    'radif' => 'Line',
    'military' => 'Military',
    'civilian' => 'Civilian',
    'location' => 'Location',
    'gender' => 'Gender',
    'male' => 'Male',
    'female' => 'Female',
    'full_name' => 'Full-name',
    'already_created' => 'Already Created',
    'id' => 'Id',
    'bast' => 'Bast',
    'tainat' => 'tainat',
    'title' => 'Title',
    'sub_department' => 'Sub department',
    'tainat_number' => 'Tainat number',
    'seprate_with_comma' => 'seprate with ',
    '',
    'employee_type' => 'Employee type',
    'job_title' => 'Job title',
    'bast_quantity' => 'Bast quantity',
    'job_description' => 'Job description (pdf)',
    'view_job_description' => 'View job description',
    'new_bast' => 'New bast',
    'sure_to_deactive_department' => 'are you sure to Deactivate this department',
    'sure_to_active_department' => 'are you sure to Activate this department',
    'advanced_search' => 'Advanced search',
    'all' => 'All',
    'excel_report' => 'Excel report',
    'delete' => 'Delete',
    'go_back' => 'Back',
    'active_deactive' => 'Active/Deactive',
    'yes' => 'Yes',
    'number' => 'Number',
    'aop' => 'Administrative Office of The Prime Minister',
    'hr_department' => 'Human Resources Department',
    'tashkilat_report' => 'Tashkilat Report',
    'nta_tashkilat_report' => 'NTA tashkilat_report',
    'islamic_emirate_of_afghanistan' => 'Islamic Emirate of Afghanistan',
    'active' => 'Active',
    'deactive' => 'Deactive',
    'something_wrong_happened' => 'Something wrong happened',
    '404' => 'Not found',
    '500' => 'Internal server error',
    'unknown' => 'Unknown',
    'father_name' => 'Father name',
    'bast_occupied_by' => 'Bast occupied by :name',
    'occupied_vacancy' => 'Occupied vacancy',
    'open_vacancy' => 'Open vacancy',
    'occupied' => 'Occupied',
    'not_occupied' => 'Not occupied',
    'show_all_departments' => 'Show all departments',
    'menu' => 'Menu',
    'show' => 'show',
    'employee_picture' => 'Employee picture',
    'view_savaneh' => 'View savaneh',
    'view_change_history' => 'View change history',
    'action_by' => 'By',
    'change_history' => 'Change history',
    'not_available' => 'Not available',
    'chart' => 'Chart',
    'horizontal' => 'Horizontal',
    'vertical' => 'Vertical',
    'tree_view' => 'Tree view',
    'org_chart' => 'Organization chart',
    'change_status' => 'Change Status',
    'sure_to_switch_status' => 'Are you sure to switch its status',
    'fullscreen' => 'Full screen',
    'loading' => 'Loading',
    'recruitment' => 'Recruitment',
    'documents' => 'Documents',
    'all_employees' => 'All employees',
    'transfers' => 'Transfers',
    'service_employees' => 'Service employees',
    'fires' => 'Fires',
    'resignees' => 'Resignees',
    'retirees' => 'Retirees',
    'waitingـwith_salary' => 'Waiting for salary',
    'ezafa_vacancy' => 'Ezafa vacancy',
    'contracts' => 'Contracts',
    'guarantees' => 'Guarantees',
    'temporary_education_documents' => 'Temporary education documents',
    'hire_date' => 'Hire date',
    'original_province' => 'Original province',
    'original_district' => 'Original district',
    'current_province' => 'Current Province',
    'current_district' => 'Current district',
    'province' => 'Province',
    'district' => 'district',
    'new_employee' => 'New employee',
    'recuirtment' => 'Recuirtment',
    'department_hasTashkill' => 'In this department there are pre-created basts',
    'create' => 'Create',
    'employee' => 'Employee',
    'ajir' => 'Ajir',
    'employees_export1_to_excel_file_title' => 'Employees report',
    'employees_excel_report_generation_failed' => 'Failed to generate employees excel report',
    'fire_date' => 'Fire date',
    'phone' => 'Phone',
    'grand_father_name' => 'Grand father name',
    'rank' => 'Rank',
    'position_title' => 'Position title',
    'other_reports' => 'Other reports...',
    'employees_with_tin' => 'Employees with tin',
    'employees_without_tin' => 'Employees without tin',
    'employees_with_tin_number_export_to_excel_file_title' => 'Employees with TIN number excel report',
    'employees_without_tin_number_export_to_excel_file_title' => 'Employees without TIN number excel report',
    'tin_number' => 'TIN number',
    'total' => 'Total',
    'employees_count' => 'Total employees',
    'ajirs_count' => 'Total ajirs',
    'militaries_count' => 'Total militaries',
    'normal' => 'Normal',
    'department_owner' => 'Department Owner',
    'executive_manager' => 'Executive Manager',
    'hr_staff' => 'HR-staff',
    'system_admin' => ' System Admin',
    'employee_edit' => 'Editing employee :name',
    'spcefications' => 'Specifications',
    'attachments' => 'Attachments',
    'hokm_moqarari' => 'Hok moqarari',
    'resign' => 'Resign',
    'firee' => 'Fire',
    'retirement' => 'Retirement',
    'salary' => 'Salary',
    'experiences' => 'Experiences',
    'trainings' => 'Trainings',
    'askari' => 'Askari',
    'promotions' => 'Promotions',
    'evaluations' => 'Evaluations',
    'punishments' => 'Punishments',
    'retributions' => 'Retributions',
    'guarantees' => 'Guarantees',
    'curriculum_vitae' => 'Curriculum Vitae',
    'barhal' => 'Curretly working',
    'servicee' => 'Service Employees',
    'other' => 'Other',
    'personal_info' => 'Personal Info',
    'last_name_en' => 'Last name (English)',
    'father_name_en' => 'Father name (English)',
    'grand_father_name_en' => 'Grand father name (English)',
    'blood_group' => 'Blood group',
    'personal_info_in_english' => 'Personal Info (English)',
    'info_about_bast' => 'Information about vacancy',
    'bast_title' => 'Vacancy title',
    'bast_mawqif' => 'Vacancy position',
    'employee_mawqif' => 'Employee position',
    'employeed_type' => 'Employeed type',
    'current_position' => 'Current position',
    'tazkira' => 'Tazkira',
    'tazkira_number' => 'Tazkira number',
    'tazkira_jold' => 'Volumn',
    'tazkira_page' => 'Page',
    'tazkira_sabt' => 'Sabt',
    'electronic_tazkira_number' => 'Tazkira number (Electronic)',
    'electronic_tazkira_year' => 'Birth year (Electronic)',
    'electronic_tazkira_month' => 'Birth month (Electronic)',
    'electronic_tazkira_day' => 'Birth date (Electronic)',
    'paper_tazkira' => 'Paper tazkira',
    'electronic_tazkira' => 'Electronic tazkira',
    'reset' => 'Reset',
    'departmentAccess' => 'Departments Access ',
    'info_about_residence' => 'Information about residence',
    'birth_year' => 'Birth year',
    'original_residence' => 'Original residence',
    'village' => 'Village',
    'current_residence' => 'Current residence',
    'educational_documents' => 'Education documents (:count)',
    'education_degree' => 'Education degree',
    'academic_field' => 'Academic field',
    'educational_institution' => 'Education institution',
    'education_country' => 'Education country',
    'graduation_year' => 'Graduation year',
    'add_new_document' => 'Add new document',
    'new' => 'New',
    'save_new_education_document' => 'Save new education document',
    'are_you_sure_to_save_new_educational_document' => 'Are you sure to save new educational document?',
    'delete_education_document' => 'Delete education document',
    'are_you_sure_to_delete_educational_document' => 'Are you sure to delete educational document?',
    'update_education_document' => 'Update education document',
    'are_you_sure_to_update_educational_document' => 'Are you sure to update educational document?',
    'document_type' => 'Document type',
    'original' => 'Original',
    'temporary' => 'Temporary',
    'contact_info' => 'Contact information',
    'employee_not_found' => 'Employee not found',
    'english_info_updated' => 'English information updated',
    'update_english_info' => 'Update english information',
    'are_you_sure_to_update_english_info' => 'Are you sure to update english information?',
    'update_contact' => 'Update contact',
    'are_you_sure_to_update_contact' => 'Are you sure to update contact?',
    'update_tazkira' => 'Update tazkira',
    'are_you_sure_to_update_tazkira' => 'Are you sure to update tazkira?',
    'month' => 'Month',
    'sub_departments' => 'Sub Departments',
    'monthly' => 'Monthly',
    'selectableDate' => 'Selectable Date',
    'fromDate' => 'from Date',
    'toDate' => 'to Date',
    'update_residence' => 'Update residence',
    'are_you_sure_to_update_residence' => 'Are you sure to update the residence?',
    'update_personal_info' => 'Update personal info',
    'are_you_sure_to_update_personal_info' => 'Are you sure to update personal info?',
    'ethnicity' => 'Ethnicity',
    'save_personal_info' => 'Save personal info',
    'other_info' => 'Other info',
    'are_you_sure_to_update_below_information' => 'Are you sure to update below information?',
    'save_employee_new_tainat' => 'Save employee new tainat',
    'tain_bast' => 'Tain bast',
    'are_you_sure_to_save_employee_new_tainat' => 'Are you sure to save employee new tainat?',
    'register_new_employee' => 'Register new employee',
    'add_employee_picture' => 'Add employee picture',
    'remove_employee_picture' => 'Remove employee picture',
    'are_you_sure_to_register_new_employee' => 'Are you sure to register new employee?',
    'bast_specifications' => 'Bast specifications',
    'barhal' => 'Barhal',
    'tabdili' => 'Tabdili',
    'application_date' => 'Application date',
    'newly_hired' => 'Newly hired',
    'person' => 'Person',
    'english' => 'English',
    'contact' => 'Contact',
    'residence' => 'Residence',
    'hokm_moqarari' => 'Hokm moqarari',
    'last_step' => 'Last step',
    'go_back_to_first_step' => 'Back to first step',
    'please_ensure_yourself_that_all_the_provided_information_is_correct_and_valid' => 'Please ensure yourself that all the provided information is correct and valid.',
    'mamoor'    =>  'Staff',
    'nezami'    =>  'Military',
    'mamoor_nezami_khedmati' =>  'Staff / Military / khedmati',
    'temporary_ajir' =>  'Temporary Ajir',
    'temporary_mamoor' =>  'Temporary Mamoor',
    'service_mamoor' =>  'Service Mamoor',
    'hokm_moqarari_date' => 'تاریخ حکم مقرری',
    'moqarari_shamsi_date' => 'تاریخ مقرری (شمسی)',
    'moqarari_qamari_date' => 'تاریخ مقرری (قمری)',
    'moqarari_date' => 'تاریخ مقرری',
    'hokm_moqarari_number' => 'نمبر حکم مقرری',
    'hokm_moqarari_attachment' => 'ضمیمه حکم مقرری',
    'birth_date' => 'تاریخ تولد',
    'date' => 'تاریخ',
    'transfered_from_other_ministries' => 'تبدیلی از سایر ادارت دولتی',
    'transfer_source' => 'مرجع تبدیلی',
    'transfer_date' => 'تاریخ تبدیلی',
    'transfer_hokm_date' => 'تاریخ حکم',
    'transfer_hokm_number' => 'نمبر حکم',
    'transfer_bast' => 'بست تبدیلی',
    'transfer_details' => 'مشخصات تبدیلی',
    'job' => 'وظیفه',
    'arrival_date' => 'تاریخ رسیدن',
    'provide_transfer_details_msg' => 'لطفا مشخصات تبدیلی این کارمند را ارائه دهید',
    'note' => 'نوت',
    'note_about_transfer' => 'نوت: اطلاعات دیگر در مورد تبدیلی کارمند را بعدأ درج کرده میتوانید.',
    'attachment' => 'ضمیمه',
    'ethnicities' => 'اقوام',
    'new_ethnicity' => 'قوم جدید',
    'no_ethnicities_available' => 'هیچ قومی موجود نیست',
    'transfer_reasons' => 'دلایل تبدیلی',
    'new_transfer_reason' => 'دلیل تبدیلی جدید',
    'no_transfer_reasons_available' => 'هیچ دلیل تبدیلی موجود نیست',
    'new_country' => 'کشور جدید',
    'no_countries_available' => 'هیچ کشوری موجود نیست',
    'new_province' => 'ولایت جدید',
    'no_provinces_available' => 'هیچ ولایتی موجود نیست',
    'new_district' => 'ولسوالی جدید',
    'no_districts_available' => 'هیچ ولسوالی موجود نیست',
    'new_village' => 'قریه/گذر جدید',
    'no_villages_available' => 'هیچ قریه/گذر موجود نیست',
    'countries' => 'کشورها',
    'districts' => 'ولسوالی ها',
    'villages' => 'قریه ها/گذز ها',
    'universities' => 'پوهنتون ها',
    'academic_fields' => 'رشته های آکادمیک',
    'academic_field_categories' => 'کتگوری رشته های آکادمیک',
    'new_university' => 'پوهنتون جدید',
    'new_academic_field_category' => 'کتگوری رشته آکادمیک جدید',
    'new_academic_field' => 'رشته آکادمیک جدید',
    'new_government_office' => 'اداره دولتی جدید',
    'government_offices' => 'ادارات دولتی',
    'government_office' => 'اداره دولتی',
    'no_government_offices_available' => 'ادارات دولتی موجود نیست',
    'education_degrees' => 'مدارک تحصیلی',
    'new_education_degree' => 'مدرک تحصیلی جدید',
    'transfer_reason' => 'دلیل تبدیلی',
    'below_similar_employees_found_in_selected_department' => 'کارمندان ذیل در ریاست انتخاب شده از قبل موجود میباشد، که با کارمند جدید مشابهت دارند.',
    'similar_employees' => 'کارمندان دارای جزییات مشابه',
    'relationship_type' => 'نوع قرابت',
    'has_relatives_in_aop' => 'آیا کارمند دارای اقارب در ریاست عمومی اداره امور میباشد؟',
    'add_new_relative' => 'اضافه کردن قرابت جدبد',
    'first_step_family_members' => 'اقارب درجه یک',
    'below_first_step_family_members_found_in_selected_department' => 'اقارب درجه یک کارمند در عین ریاست دریافت شد.',
    'from_date' => 'از تاریخ',
    'to_date' => 'تا تاریخ',
    'description' => 'ملاحظات',
    'transfer_reason' => 'دلیل تبدیلی',
    'next' => 'بعدی',
    'previous' => 'قبلی',
    'tashkil_bast_is_occupied' => 'بست اشعال شده است',
    'special_employee_registration' => 'ثبت کارمند فوق العاده',
    'similar_employees_exist_do_you_want_to_register_new_employee' => 'کارمندان مشابه و یا تکراری وجود دارد!!! آیا باز هم مطمئن هستید که کارمند جدید ذخیره شود؟',
    'your_action_will_be_recorded' => 'این عمل تان ثبت سیستم میشود.',
    'is_required' => 'آیا الزامی است؟',
    'register_special_employee' => 'ثبت کارمند فوق العاده',
    'special_employee_registration_warning_msg' => 'این تنها برای ثبت کارمندان فوق العاده است که دارای معلومات کامل نمی باشند.',
    'similar_recrods_exist_do_you_want_to_register_new_employee' => 'کارمندان مشابه به این کارمند موجود است، آیا مطمئن هستید که کارمند ثبت شود؟',
    'listAllDepartment' => 'List of all Departments',
    'doesnt_have_email' => 'does not have email',
    'roles_and_permissions' => 'Module & Permissions',
    'select_employee_first' => 'Please select an Employee first',
    'select_department_first' => 'Please select department first',
    'access_not_allowed' => 'You do not have permission to access',
    'user_already_has_account' => 'An account has already been created for this person',
    'has_card' => 'Has Card',
    'not_has_card' => 'Without Card',
    'employees' => 'Employees',
    'total_employees' => 'Total Employee',
    'card_period' => 'Card Period',
    'specifications' => 'Generalities',
    'fire' => 'Fire',
    'makafats' => 'Makafats',
    'cv' => 'CV',
    'capacity_building' => 'Capacity building',
    'data_settings' => 'Master data',
    'barhal_employees' => 'Barhal employees',
    'contractors' => 'Contractors',
    'tanqises' => 'Tanqisat',
    'cards' => 'Cards',
    'card_type_status' => 'Card type status',
    'card_period_status' => 'Card period status',
    'card_lists_export_to_excel_file_title' => 'list of printed cards (:period)',
    'tainat_date' => 'Tainat date',
    'update_ethnicity' => 'Update ethnicity',
    'update_moqarari_approval_authority' => '',
    'fetching_content' => 'Loading...',
    'remarks' => 'Remarks',
    'period_number' => 'Period Number',
    'periods' => 'Periods',
    'are_you_sure_to_change status' => 'Are you sure to change status?',
    'card_period_status_change_warning' => 'If it get changed to active mode, all previous card periods will be disabled',
    'card_type' => 'Card Type',
    'card_types' => 'Card Types',
    'successful' => 'Successful',
    'successfully_removed' => 'Successfully removed',
    'hire_year' => 'Hire year',
    'fire_year' => 'Fire year',
    'resign_year' => 'Resign year',
    'retire_year' => 'Retire year',
    'tanqis_year' => 'Tanqis year',
    'transfered_employees' => 'Transfered employees',
    'service_employee_list' => 'service employees',
    'contract_employees_list' => 'Contract employees',
    'fired_employees_list' => 'Fired employees',
    'resigned_employees_list' => 'Resigned employees',
    'retired_employees_list' => 'Retired employees',
    'tanqis_employees_list' => 'Tanqis employees',
    'guaranteed_employees_list' => 'Guaranteed employees',
    'cards_periods' => 'Card Periods',
    'two_sides' => 'Two Sides',
    'saved_year' => 'record year',
    'period' => 'Period',
    'front_card_background' => 'Card Front Background',
    'back_card_background' => 'Card back Background',
    'front_attachment_url' => 'Card Front Background',
    'back_attachment_url' => 'Card back Background',
    'card_type_status_change_warning' => 'If a card type is changed to inactive mode, card printing of this type will be disabled',
    'store_attachment_failed' => 'Attachment storage failed',
    'no_active_period_found' => 'No active period found',
    'emp_has_no_card_or_card_has_not_set' => 'The selected employee is either not a cardholder or a card has not been assigned to this individual',
    'print_date' => 'print date',
    'secondary' => 'secondary',
    'print_type' => 'print type',
    'normal_card' => 'Noramal Card',
    'secondary_card' => 'Secondary Card',
    'cards_water_mark' => 'print sample card',
    'print_test_card' => 'print sample card',
    'print_card' => 'print card',
    'printed_cards' => 'Printed cards',
    'setup_card' => 'Setup card',
    'please_set_an_active_period_first' => 'Please set an active period',
    'select_card_type' => 'select card type',
    'card_reference_id' => 'card reference id',
    'enter_card_no' => 'Enter card number',
    'choose_image' => 'choose image',
    'please_unlock_the_card_first' => 'Please unlock the card first',
    'uncolpleeted_data' => 'uncolpleeted data',
    'not_successful' => 'not successfully',
    'unlock_card' => 'unlock card',
    'reason' => 'Reason',
    'leave_types' => 'types of leave',
    'profile' => 'Profile',
    'enter_email' => 'enter your email address',
    'password' => 'Password',
    'forgot_password' => 'Forgot your password?',
    'create_account' => 'Create Account',
    'change_pass_create_account' => 'Change Password / Create Account',
    'create_account_info_text' => 'To change the password or create a user account, please visit the IT departments general management of applications or call 3012, 3009.',
    'remember_me' => 'Remember Me',
    'read_me' => 'EIA, General Directorate of Affairs',
    'aop' => 'General Directorate of Affairs',
    'itd' => 'Prepared by: Directorate of Information Technology',
    'login' => 'login',
    'logout' => 'logout',
    'leave_form' => 'Leave Form',
    'job_description' => 'Job Description',
    'download' => 'Download',
    'forms' => 'Forms',
    'health_form' => 'Medical Form',
    'mis_systems_access_form' => 'Information systems access form',
    'create_domain_user_form' => 'Create Domain User Form',
    'domain_user_transfer_form' => 'Transfare Domain User Form',
    'domain_user_change_password_form' => 'Change domain user password form',
    'create_official_email_address_form' => 'Create official email address form',
    'create_joint_email_address_form' => 'Create official joint email address form',
    'change_official_email_password_form' => 'Change official email address form',
    'usb_form' => 'USB-port access form',
    'cisco_form' => 'Sisco form',
    'press_form' => 'Press Form',
    'general_holidays' => 'General Holidays',
    'edit_user' => 'Edit user',
    'barhal_basts' => 'Basts',
    'mafaowq_bast' => 'Mafaoq',
    'faowq_bast' => 'Faoq',
    'bast_1' => 'First',
    'bast_2' => 'Second',
    'bast_3' => 'Third',
    'bast_4' => 'Fourth',
    'bast_5' => 'Fifth',
    'bast_6' => 'Sixth',
    'bast_7' => 'Seventh',
    'bast_8' => 'Eigth',
    'attendance_in_out_count' => 'Number of employees including electronic attendance and without electronic attendance',
    'attendance_in_count' => 'Number of employees on electronic attendance',
    'attendance_out_count' => 'Number of employees in attendance book',
    'no_announcements' => 'No announcements',
    'attendance_out_count' => 'Number of employees without electronic attendance',
    'view_design_not_exist' => 'page design not found',
    'alirutba' => 'high rank employees',
    'karmand' => 'employees',
    'total_emps_has_card' => 'total percentage of employees who have a card',
    'total_eligible_barhal_emp_for_card' => 'total of barhal employees eligible for card',
    'total_eligible_barhal_emp_has_card' => 'total of barhal eligible employees with card',
    'total_eligible_barhal_emp_has_no_card' => 'total of barhal employees eligible for card but without card',
    'latest_announcements' => 'latest announcements',
    'create_permission' => 'Create permission',
    'create_role' => 'Create role',
    'not_valid_number' => 'The number is not valid',
    'announcement' => 'Announcement',
    'students' => 'students',
    'included_not_included' => 'include / not include',
    'retype_pass' => 'Retype password',
    "preRoles" => "predefined roles",
    "preRoleManagement" => "pre-defined role management",
    "request_leave" => "leave request",
    "solar_hijri" => "Solar Hijri",
    "gregorian" => "Gregorian",
    "requested_date" => "request date",
    "request_text" => "request text",
    "response_text" => "Response text",
    "request" => "request",
    "accepted" => "approved",
    "rejected" => "rejected",
    "accepted_internally" => "internally accepted",
    "request_leave_page" => "leave request page",
    "leave_request_delete_policy_text" => "All requests in rejected status or request status before approval by the relevant head-department/department will be deleted after 7 days.",
    "at_leave_day_location" => "Location during leave days",
    "the_number_of_days_of_this_leavetype_has_completed" => "The number of days of this type of leave has been completed or the remaining days are less than the requested days",
    "requested_mgmt" => "Request Management",
    "my_requests" => "My requests",
    "approve_reject" => "approve / reject",
    "leave_request_en" => "Leave Rquest",
    'year' => 'year',
    'years' => 'years',
    'month' => 'month',
    'months' => 'months',
    'week' => 'week',
    'weeks' => 'weeks',
    'day' => 'day',
    'days' => 'days',
    'hour' => 'hour',
    'hours' => 'hours',
    'minute' => 'minute',
    'minutes' => 'minutes',
    'second' => 'second',
    'seconds' => 'seconds',
    'ago' => 'ago',
    'from_now' => 'from now',
    'view_all' => 'View all',
    'shamel' => 'Including electronic attendance',
    'not_shamel' => 'Not including electronic attendance',
    'contractual' => 'Contractual',
    'contractor' => 'Contractual',
    'add_student' => 'register new student',
    'student_shift' => 'student Shift',
    'study_period_start_date' => 'study Period Start Date',
    'study_period_end_date' => 'study Period End Date',
    'student_doc_form' => 'student ducument Form',
    'student_studieng_period_days' => 'Student Studieng Period Days',
    'semester' => 'Semester',
    'morning' => 'morning',
    'noon' => 'noon',
    'afternoon' => 'afternoon',
    'pending' => 'pending',
    'education_level' => 'Education Level',
    'registered_at_machines' => 'Registered at machines',
    'not_registered_at_machines' => 'not Registered at macines',
    'enrolled_at_shift' => 'enrolled at shift',
    'in_shift' => 'in shift',
    'bachelor' => 'Bachelor',
    'master' => 'Master',
    'phd' => 'Doctorate',
    'unnamed_file' => 'unnamed file',
    'promotion_record_and_p2_Form' => 'promotion record and p2 form',
    'four_page_form' => 'four-page form',
    'acknowledgment_letter' => 'acknowledgment letter',
    'recognition_reward' => 'recognition reward',
    'disciplinary_actions' => 'disciplinary actions',
    'delete_attachment' => 'delete attachment',
    'ready_for_retire' => 'Retire Ready',
    'work_experience' => 'work experience',
    'days_of_the_week' => 'Days of the week',
    'student_registration_complete_status' => 'Student has registered in the attendance machines',
    'ready_for_promotion' => 'ready for promotion',
    'promotion_ready_employees_list' => 'promotion ready employees list',
    'retire_ready_employees_list' => 'retire ready employees list',
    'promotion_date' => 'Promotion date',
    'edit_student' => 'Edit Student',
    'active_record_found' => 'Active record found',
    'barhal_student' => 'active enrolled Student',
    'employee_evaluation_recommendations' => 'Employee evaluation recommendations',
    'update_evaluation_recommendation' => 'update evaluation recommendation',
    'new_employee_evaluation_result' => 'new evaluation result',
    'new_evaluation_recommendation' => 'new evaluation recommendation',
    'no_employee_evaluation_recommendations_available' => 'No employee evaluation recommendations available',
    'no_employee_evaluation_results_available' => 'No employee evaluation results available',
    'evaluation_recommendation' => 'Evaluation Recommendation',
    'nta_basts' => 'NTA Basts',
    'card_rfid' => 'Card RFID',
    'saturday' => 'Saturday',
    'sunday' => 'sunday',
    'monday' => 'monday',
    'tuesday' => 'tuesday',
    'wednesday' => 'wednesday',
    'thursday' => 'thursday',
    'friday' => 'friday',
    'not' => 'no',
    'start_date' => 'start date',
    'end_date' => 'end date',
    'attendance_department' => 'General Departments of Attendance',
    'resign_number' => 'Resignation order number',
    'copy_to_new_tashkil' => 'Copy into new tashkil',
    'page_of_copying_tashkil_of_a_year_to_another_year' => 'Page of copying the tashkil of one year to another',
    'copy_tashkil' => 'Copy Tashkil',
    'please_fill_required_feilds_first' => 'Please fill in the required fields first',
    'new_nta_grid' => 'new NTA grid',
    'update_nta_grid' => 'update NTA grid',
    'nta_steps' => 'NTA steps',
    'new_nta_step' => 'new NTA step',
    'update_nta_step' => 'update NTA step',
    'nta_salary' => 'NTA salary',
    'new_nta_salary' => 'new NTA salary',
    'update_nta_salary' => 'update NTA salary',
    'currency' => 'currency',
    'internal_office' => 'Internal Office',
    'tashkilat_statistical_report' => 'Tashkilat statistical report',
    'employees_bast' => 'Employees basts',
    'military_bast' => 'Military basts',
    'ajir_bast' => 'Ajir basts',
    'complete' => 'Complete',
    'tashkil_statistical_report' => 'Tashkil statistical report (:date)',
    'dagar_general_loy_passwal' => 'ډګرجنرال/لوی پاسوال',
    'toran_general_passwal' => 'تورن جنرال/پاسوال',
    'brid_general_mal_passwal' => 'برید جنرال/مل پاسوال',
    'dagarwal_samonwal' => 'ډګروال/سمونوال',
    'dagarman_samonmal' => 'ډګرمن/سمونمل',
    'jagran_samonyar' => 'جګړن/سمونیار',
    'toran_sarman' => 'تورن/څارمن',
    'lomray_bridman_lomray_sarman' => 'لمړی بریدمن/لمړی څارن',
    'dwaham_bridman_dowaham_saran' => 'دوهم بریدمن/دوهم څارن',
    'lomray_satanman' => 'لمړی ساتنمن',
    'kharij_rotba' => 'خارج رتبه',
    'total_tashkil' => 'general total tashkil of year (:date)',
    'server_under_maintenance' => 'The server is under maintenance',
    'maintenance_msg' => 'The system is currently being updated and technical issues are being resolved. Please check back later',
    'general_info_report' => 'General information report',
    'education_info' => 'Education Information',
    'duty_info' => 'Duty information',
    'contact_information' => 'Contact information',
    'card_status' => 'Card status',
    'employees_list' => 'employees list :card_status',
    'administrative_office' => 'Administrative Office',
    "department_response_text" => "Relevant department's confirmation remarks",
    "hr_response_text" => "Human Resources Statement",
    "leave_request" => "Leave Request",
    "new_stations_are_forbidden" => 'Saving new station is prohibited',
    'maintenance_msg_start' => 'The system is currently updating to the new version',
    'degree_determination' => 'degree determination',
    'rank_determination' => 'rank determination',
    'are_you_sure_to_update_ajir_degree' => 'Are you sure to update ajir degree?',
    'are_you_sure_to_update_employee_rank' => 'Are you sure to update employee rank?',
    'backloria' => 'Backloria',
    'fawq_backloria' => 'Fawq backloria',
    'first_period' => 'First period',
    'second_period' => 'Second period',
    'fix_photos' => 'fix photos',
    'employee_taken_leaves' => 'Previous department leave',
    'chairman' => 'Chairman',
    'rejected_by_hr' => 'Rejected By HR',
    'image' => 'image',
    'exceeds_from_valide_acceptable_amount' => 'Exceeds from acceptable amount',
    'leave_request_invalid_for_present_employee'=>'Leave request invalid for present employee',
    'form' => 'form',
    'update_form' => 'update form',
];
