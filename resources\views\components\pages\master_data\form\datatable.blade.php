@props(['data' => []])
<x-tools.utilities.datapager.ajax_data_pager_utils :data="$data" />

<table class="table table-cust" id="form_datatable">
    <thead style="background-color: #fff9e5;">
        <tr>
            <th>{{ __('general.id') }}</th>
            <th>{{ __('general.form_name') }}</th>
            <th>{{ __('general.status') }}</th>
            <th>{{ __('general.created_at') }}</th>
            <th>{{ __('general.actions') }}</th>
        </tr>
    </thead>
    <tbody>
        @if (!is_null($data) && count($data) > 0)
            @foreach ($data as $item)
                <tr id="{{ 'form-' . encrypt($item->id) }}">
                    <td>{{ $loop->iteration }}</td>
                    <td>{{ $item->name }}</td>
                    <td>{{ $item->status == 1 ? __('general.active') : __('general.deactive') }}</td>
                    <td>{{ dateTo($item->created_at, 'shamsi', false) }}</td>
                    <td>
                        <a href="{{ route('master-data.job-description-template.edit', encrypt($item->id)) }}" type="button"
                            class="btn btn-icon btn-sm btn-outline-vimeo tashkil-edit-click">
                            <i class="fa fa-edit"></i>
                        </a>
                    </td>
                </tr>
            @endforeach
        @else
            <tr>
                <td colspan="10">
                    <p class="text-center">{{ __('general.not_found') }}</p>
                </td>
            </tr>
        @endif
    </tbody>
</table>
