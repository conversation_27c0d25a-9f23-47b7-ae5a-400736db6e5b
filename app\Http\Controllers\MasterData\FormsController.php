<?php

namespace App\Http\Controllers\MasterData;

use App\Http\Controllers\Controller;
use App\Http\Requests\MasterData\Form\FormStoreRequest;
use App\Http\Requests\MasterData\Form\FormUpdateRequest;
use App\Repositories\Attachment\AttachmentRepository;
use App\Repositories\MasterData\FormRepository;
use App\Traits\ResponseTrait;
use Illuminate\Http\Request;

class FormsController extends Controller
{
    use ResponseTrait;

    private $formRepository;
    private $attachmentRepository;

    public function __construct(FormRepository $formRepository, AttachmentRepository $attachmentRepository)
    {
        $this->middleware('auth');
        $this->formRepository = $formRepository;
        $this->attachmentRepository = $attachmentRepository;
    }

    public function index(Request $request)
    {
        // per page
        $perPage = $request->input('perPage', 10);
        $data = $this->formRepository->getForms($perPage);

        // check if request is ajax
        if ($request->ajax()) {
            return view('components.pages.master_data.form.datatable', ['data' => $data]);
        } else {
            return view('pages.master_data.form.list', ['data' => $data, 'total_count' => count($this->formRepository->getAllForms()), 'pager' => withPaginate(['searchRouteName' => 'master-data.form.search'])]);
        }
    }

    public function searchForms(Request $request)
    {
        $perPage = $request->input('perPage', 10);
        $data = $this->formRepository->searchForms($request, $perPage);

        return view('components.pages.master_data.form.datatable', ['data' => $data]);
    }

    public function showCreateForm()
    {
        $data['record'] = null;
        return view('pages.master_data.form.form', ['data' => $data]);
    }

    public function store(FormStoreRequest $request)
    {
        //upload the form first and the append the url in database.
        // path example => storage/app/public/documents/hr-attachments/1402/forms/1401_123123_form.pdf
        try {
            $filename = null;
            if ($request->hasFile('form')) {
                $file = $request->file('form');
                $shamsiYear = getCurrentShamsiYear();
                $destinationPath = '/documents/hr-attachments/' . $shamsiYear . '/forms';
                $filename = $shamsiYear . '_' . time() . '_' . 'form' . '.' . $file->getClientOriginalExtension();
                $file->move(storage_path('app/public') . $destinationPath, $filename);
            }
            $record = $this->formRepository->store($request, $filename);
            if ($record != false) {
                return redirect()->route('master-data.forms.all')->with('success', trans('general.created_success'));
            }
            return redirect()->back()->with('error', trans('general.created_fail'));
        } catch (\Exception $ex) {
            return redirect()->back()->with('error', trans('general.created_fail'), $ex);
        }
    }

    public function showEditForm($id)
    {
        try {
            $data['record'] = $this->formRepository->getFormById(decrypt($id));
            if (!is_null($data['record']) && !empty($data['record']->path)) {
                $data['path'] = $this->attachmentRepository->getPathForAttachment('forms', false, true, false, $data['record']->path);
            } else {
                $data['path'] = null;
            }
            if (is_null($data['record'])) {
                abort(404);
            }
            return view('pages.master_data.form.form', ['data' => $data]);
        } catch (\Exception $ex) {
            abort(500);
        }
    }

    public function update(FormUpdateRequest $request, $id)
    {
        try {
            $record = $this->formRepository->getFormById(decrypt($id));
            if(is_null($record)){
                abort(404);
            }

            $filename = $record->path;
            if($request->hasFile('form')){
                // delete the previous file if exists
                if(!empty($record->path)){
                    $this->attachmentRepository->deleteAttachmentFileByName($record->path, true);
                }
                // upload the new file
                $file = $request->file('form');
                $shamsiYear = getCurrentShamsiYear();
                $destinationPath = '/documents/hr-attachments/' . $shamsiYear . '/forms';
                $filename = $shamsiYear . '_' . time() . '_' . 'form' . '.' . $file->getClientOriginalExtension();
                $file->move(storage_path('app/public') . $destinationPath, $filename);
            }

            // update the record
            $updated = $this->formRepository->update($request, $filename, decrypt($id));
            if($updated){
                return redirect()->back()->with('success', trans('general.updated_success'));
            }
            return redirect()->back()->with('error', trans('general.updated_fail'));
        } catch (\Exception $ex) {
            return redirect()->back()->with('error', trans('general.updated_fail'), $ex);
        }
    }
}
