<?php

namespace App\Http\Controllers;

use App\Repositories\MasterData\FormRepository;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    private $formRepository;
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(FormRepository $formRepository)
    {
        $this->middleware('auth');
        $this->formRepository = $formRepository;
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {

        $is_normal_user = auth()->user()->type;
        // get acitve form from db
        $data['forms'] = $this->formRepository->getActiveForms();

        if (in_array($is_normal_user, [1, 2, 3])) { // normal user home
            return view('att_user_home', ['data' => $data]);
        }
        // management user home
        return view('home', ['data' => $data]);
    }
}
