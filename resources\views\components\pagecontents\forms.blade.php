@props(['forms' => []])
<div class="card">
    <div class="card-header d-flex align-items-center justify-content-between">
        <h5 class="card-title m-0 me-2">{{ __('general.forms') }}</h5>
    </div>
    <div class="card-body">
        <ul class="p-0 m-0">
            @if(isset($forms) && $forms->count() > 0)
                @foreach($forms as $form)
                    <li class="d-flex pb-1">
                        <div class="avatar flex-shrink-0 me-3">
                            <img src="{{ asset('assets/img/icons/unicons/wallet.png') }}" alt="User" class="rounded">
                        </div>
                        <div class="d-flex w-100 flex-wrap align-items-center justify-content-between gap-2 border-bottom">
                            <div class="me-2">
                                <h7 class="mb-0">{{ $form->name }}</h7>
                            </div>
                            <div class="user-progress d-flex align-items-center gap-1">
                                <h6 class="mb-0"></h6>
                                <a href="{{ route('forms.download.database', ['id' => encrypt($form->id)]) }}"
                                    target="_blank" title="{{ __('general.open_in_new_tab') }}">
                                    <button class="btn btn-xs rounded-pill btn-label-secondary p-1 px-2">
                                        {{ __('general.download') }}
                                    </button>
                                </a>
                            </div>
                        </div>
                    </li>
                @endforeach
            @else
                <li class="d-flex pb-1">
                    <div class="text-center w-100">
                        <p class="mb-0 text-muted">{{ __('general.not_available') }}</p>
                    </div>
                </li>
            @endif
        </ul>
    </div>
</div>
