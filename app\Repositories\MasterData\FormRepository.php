<?php

namespace App\Repositories\MasterData;

use App\Models\MasterData\Form;
use Illuminate\Support\Facades\DB as FacadesDB;

class FormRepository{
    
    public function getForms($perPage = 10){
        try{
            $query = $this->getFormsQuery();
            $data = $query->paginate($perPage);
            return $data;
        }catch(\Exception $ex){
            throw $ex;
        }
    }

    public function getFormsQuery(){
        try{
            $query = FacadesDB::table('forms')->select('id', 'name_ps', 'name_dr', 'name_en', 'created_at', 'name_' . app()->getLocale() . ' as name', 'status');
            return $query;
        }catch(\Exception $ex){
            throw $ex;
        }
    }

    public function getAllForms(){
        try{
            return FacadesDB::table('forms')->get();
        }catch(\Exception $ex){
            return [];
        }
    }

    public function searchForms($perPage)
    {
        try{
            $query = $this->getFormsQuery();
            $data = $query->paginate($perPage);
            return $data;
        }catch(\Exception $ex){
            return [];
        }
    }

    public function store($request, $path){
        try{
            $record = new Form;
            $record->name_ps = $request->name_ps;
            $record->name_dr = $request->name_dr;
            $record->name_en = $request->name_en;
            $record->path = $path;
            $record->save();

            return true;
        }catch(\Exception $ex){
            throw $ex;
        }
    }

    public function getFormById($id){
        try{
            return Form::find($id);
        }catch(\Exception $ex){
            throw $ex;
        }
    }
}